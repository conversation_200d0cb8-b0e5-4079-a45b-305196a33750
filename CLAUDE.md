# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

```bash
# Development
pnpm dev          # Start dev server with TurboPack on http://localhost:3000

# Database
pnpm db:generate  # Generate Prisma Client after schema changes
pnpm db:push      # Push schema changes to database

# Build & Production
pnpm build        # Build for production
pnpm start        # Start production server

# Code Quality
pnpm lint         # Run ESLint
```

## Architecture Overview

This is a **Next.js 15** application using **Clean Architecture** principles for the API layer.

### API Structure (`/src/app/api/`)

The API uses **Hono framework** with a modular pattern:

```
entity/
├── dto/           # Data Transfer Objects with Zod validation
├── repository/    # Data access layer (interface + Prisma implementation)
├── use-cases/     # Business logic (one file per operation)
├── routes.ts      # Hono route definitions
└── middleware/    # Route-specific middleware
```

All API routes are handled through `/api/[[...route]]/route.ts` using Hono's catch-all routing.

### Key Technologies

- **Edge Runtime**: APIs run on Vercel Edge for optimal performance
- **Prisma ORM**: Database access with PostgreSQL (Neon serverless)
- **Zod**: Runtime validation for all API inputs/outputs
- **React Query**: Data fetching and caching on the frontend
- **UI Components**: Radix UI primitives in `/src/components/ui/`

### Database Schema

Prisma schema is modularized:
- `prisma/schema.prisma` - Main configuration
- `prisma/user.prisma` - User model
- `prisma/property.prisma` - Property model
- `prisma/offer.prisma` - Offer model

After schema changes, run `pnpm db:push` to update the database.

### Coding Standards

1. **API Validation**: Always use Zod schemas for request/response validation
2. **UI Components**: Use components from `/src/components/ui/` instead of raw HTML elements
3. **Error Handling**: Return standardized error responses with proper HTTP status codes
4. **Type Safety**: Leverage TypeScript and generated Prisma types
5. **Repository Pattern**: Access data only through repository interfaces, not direct Prisma calls in use cases

### Adding New Features

To add a new entity (e.g., "bookings"):

1. Create schema in `prisma/booking.prisma`
2. Run `pnpm db:generate` and `pnpm db:push`
3. Create API structure:
   ```
   src/app/api/[[...route]]/bookings/
   ├── dto/
   │   ├── booking.dto.ts
   │   ├── create-booking.dto.ts
   │   └── update-booking.dto.ts
   ├── repository/
   │   ├── booking.repository.ts
   │   └── prisma-booking.repository.ts
   ├── use-cases/
   │   ├── create-booking.use-case.ts
   │   ├── get-booking.use-case.ts
   │   ├── list-bookings.use-case.ts
   │   ├── update-booking.use-case.ts
   │   └── delete-booking.use-case.ts
   ├── routes.ts
   └── index.ts
   ```
4. Register routes in main API handler

### Environment Setup

The project uses PostgreSQL via Neon. Ensure `DATABASE_URL` is set in your environment variables.