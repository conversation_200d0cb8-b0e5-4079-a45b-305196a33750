import React from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import Link from 'next/link'
import { 
  User, 
  MapPin, 
  Heart, 
  Settings, 
  Edit3, 
  Phone, 
  Mail, 
  Calendar,
  Home,
  Bed,
  Bath,
  Square
} from 'lucide-react'

export const runtime = 'edge'

// Mock user data
const mockUser = {
  name: '<PERSON>',
  email: '<EMAIL>',
  phone: '(*************',
  location: 'New York, NY',
  joined: 'January 2024',
  avatar: '/placeholder-avatar.jpg'
}

// Mock saved properties
const savedProperties = [
  {
    id: '1',
    title: 'Modern Downtown Apartment',
    price: '$2,500/month',
    location: 'Downtown, City Center',
    bedrooms: 2,
    bathrooms: 2,
    area: '1,200 sq ft',
    type: 'Apartment',
    savedDate: '2024-01-15'
  },
  {
    id: '2',
    title: 'Cozy Suburban Home',
    price: '$3,200/month',
    location: 'Suburbs, Green Valley',
    bedrooms: 3,
    bathrooms: 2,
    area: '1,800 sq ft',
    type: 'House',
    savedDate: '2024-01-10'
  }
]

function SavedPropertyCard({ property }: { property: typeof savedProperties[0] }) {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-3">
          <div className="flex-1">
            <h3 className="font-semibold text-lg mb-1">
              <Link href={`/listings/${property.id}`} className="hover:text-blue-600">
                {property.title}
              </Link>
            </h3>
            <div className="flex items-center text-slate-600 mb-2">
              <MapPin className="h-4 w-4 mr-1" />
              <span className="text-sm">{property.location}</span>
            </div>
            <p className="text-xl font-bold text-blue-600">{property.price}</p>
          </div>
          <Badge variant="outline">{property.type}</Badge>
        </div>
        
        <div className="flex justify-between items-center text-sm text-slate-600 mb-3">
          <div className="flex items-center gap-4">
            <div className="flex items-center">
              <Bed className="h-4 w-4 mr-1" />
              <span>{property.bedrooms} bed</span>
            </div>
            <div className="flex items-center">
              <Bath className="h-4 w-4 mr-1" />
              <span>{property.bathrooms} bath</span>
            </div>
            <div className="flex items-center">
              <Square className="h-4 w-4 mr-1" />
              <span>{property.area}</span>
            </div>
          </div>
        </div>
        
        <div className="flex justify-between items-center">
          <span className="text-xs text-slate-500">
            Saved on {new Date(property.savedDate).toLocaleDateString()}
          </span>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              Remove
            </Button>
            <Link href={`/listings/${property.id}`}>
              <Button size="sm">View Details</Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default function ProfilePage() {
  return (
    <main className="min-h-screen bg-slate-50">
      <div className="container mx-auto py-8 px-4">
        {/* Profile Header */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-6 items-start">
              <div className="w-24 h-24 bg-slate-200 rounded-full flex items-center justify-center">
                <User className="h-12 w-12 text-slate-500" />
              </div>
              
              <div className="flex-1">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                  <div>
                    <h1 className="text-3xl font-bold text-slate-900 mb-2">{mockUser.name}</h1>
                    <div className="flex items-center text-slate-600 mb-1">
                      <Mail className="h-4 w-4 mr-2" />
                      <span>{mockUser.email}</span>
                    </div>
                    <div className="flex items-center text-slate-600 mb-1">
                      <Phone className="h-4 w-4 mr-2" />
                      <span>{mockUser.phone}</span>
                    </div>
                    <div className="flex items-center text-slate-600 mb-1">
                      <MapPin className="h-4 w-4 mr-2" />
                      <span>{mockUser.location}</span>
                    </div>
                    <div className="flex items-center text-slate-600">
                      <Calendar className="h-4 w-4 mr-2" />
                      <span>Member since {mockUser.joined}</span>
                    </div>
                  </div>
                  
                  <Button className="flex items-center gap-2">
                    <Edit3 className="h-4 w-4" />
                    Edit Profile
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Profile Tabs */}
        <Tabs defaultValue="saved" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="saved" className="flex items-center gap-2">
              <Heart className="h-4 w-4" />
              Saved Properties
            </TabsTrigger>
            <TabsTrigger value="listings" className="flex items-center gap-2">
              <Home className="h-4 w-4" />
              My Listings
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Settings
            </TabsTrigger>
          </TabsList>

          {/* Saved Properties Tab */}
          <TabsContent value="saved" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-slate-900">
                Saved Properties ({savedProperties.length})
              </h2>
              <Link href="/listings">
                <Button variant="outline">Browse More Properties</Button>
              </Link>
            </div>
            
            {savedProperties.length > 0 ? (
              <div className="grid md:grid-cols-2 gap-6">
                {savedProperties.map((property) => (
                  <SavedPropertyCard key={property.id} property={property} />
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="p-12 text-center">
                  <Heart className="h-12 w-12 mx-auto mb-4 text-slate-400" />
                  <h3 className="text-lg font-semibold mb-2">No saved properties yet</h3>
                  <p className="text-slate-600 mb-4">
                    Start browsing properties and save your favorites to see them here.
                  </p>
                  <Link href="/listings">
                    <Button>Browse Properties</Button>
                  </Link>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* My Listings Tab */}
          <TabsContent value="listings" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-slate-900">My Listings</h2>
              <Link href="/listings/create">
                <Button>Create New Listing</Button>
              </Link>
            </div>
            
            <Card>
              <CardContent className="p-12 text-center">
                <Home className="h-12 w-12 mx-auto mb-4 text-slate-400" />
                <h3 className="text-lg font-semibold mb-2">No listings yet</h3>
                <p className="text-slate-600 mb-4">
                  Create your first property listing to start attracting potential tenants or buyers.
                </p>
                <Link href="/listings/create">
                  <Button>Create Listing</Button>
                </Link>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <h2 className="text-2xl font-bold text-slate-900">Account Settings</h2>
            
            <div className="grid gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">Full Name</label>
                      <input
                        type="text"
                        defaultValue={mockUser.name}
                        className="w-full px-3 py-2 border border-slate-300 rounded-lg"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Email</label>
                      <input
                        type="email"
                        defaultValue={mockUser.email}
                        className="w-full px-3 py-2 border border-slate-300 rounded-lg"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Phone</label>
                      <input
                        type="tel"
                        defaultValue={mockUser.phone}
                        className="w-full px-3 py-2 border border-slate-300 rounded-lg"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Location</label>
                      <input
                        type="text"
                        defaultValue={mockUser.location}
                        className="w-full px-3 py-2 border border-slate-300 rounded-lg"
                      />
                    </div>
                  </div>
                  <Button>Update Information</Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Change Password</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Current Password</label>
                    <input
                      type="password"
                      className="w-full px-3 py-2 border border-slate-300 rounded-lg"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">New Password</label>
                    <input
                      type="password"
                      className="w-full px-3 py-2 border border-slate-300 rounded-lg"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Confirm New Password</label>
                    <input
                      type="password"
                      className="w-full px-3 py-2 border border-slate-300 rounded-lg"
                    />
                  </div>
                  <Button>Change Password</Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Notification Preferences</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Email Notifications</h4>
                      <p className="text-sm text-slate-600">Receive updates about your listings and saved properties</p>
                    </div>
                    <input type="checkbox" defaultChecked className="h-4 w-4" />
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Marketing Emails</h4>
                      <p className="text-sm text-slate-600">Receive information about new features and promotions</p>
                    </div>
                    <input type="checkbox" className="h-4 w-4" />
                  </div>
                  <Button>Save Preferences</Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </main>
  )
} 