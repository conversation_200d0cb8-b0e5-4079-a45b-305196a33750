import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { listingRoutes } from './listings/routes';
import { propertyRoutes } from './properties/routes';

export const runtime = 'edge';

const app = new Hono().basePath('/api');

// Add routes for proper type inference
app.get('/hello', (c) => {
  return c.json({
    message: 'Hello Next.js!',
  });
});

const routes = app
  .route('/listings', listingRoutes)
  .route('/properties', propertyRoutes);

// Export the app type for RPC
export type AppType = typeof routes;

// Export handlers for Next.js
export const GET = handle(routes);
export const POST = handle(routes);
export const PUT = handle(routes);
export const DELETE = handle(routes);