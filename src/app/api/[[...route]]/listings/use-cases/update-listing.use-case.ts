import type { ListingDTO } from '../dto/listing.dto';
import type { UpdateListingDTO } from '../dto/update-listing.dto';
import type { ListingRepository } from '../repository/listing.repository';

export class UpdateListingUseCase {
  constructor(private repository: ListingRepository) {}

  async execute(id: number, data: UpdateListingDTO): Promise<ListingDTO | null> {
    // Check if listing exists first
    const existingListing = await this.repository.findById(id);
    if (!existingListing) {
      return null;
    }
    
    return this.repository.update(id, data);
  }
} 