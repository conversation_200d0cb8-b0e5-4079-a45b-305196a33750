import type { FilterListingDTO } from '../dto/filter-listing.dto';
import type { ListingDTO } from '../dto/listing.dto';
import type { ListingRepository } from '../repository/listing.repository';

export class ListListingsUseCase {
  constructor(private repository: ListingRepository) {}

  async execute(filters?: FilterListingDTO): Promise<ListingDTO[]> {
    return this.repository.findAll(filters);
  }
} 