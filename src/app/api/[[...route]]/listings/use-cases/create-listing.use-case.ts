import type { CreateListingDTO } from '../dto/create-listing.dto';
import type { ListingDTO } from '../dto/listing.dto';
import type { ListingRepository } from '../repository/listing.repository';

export class CreateListingUseCase {
  constructor(private repository: ListingRepository) {}

  async execute(data: CreateListingDTO): Promise<ListingDTO> {
    // Here you can add additional business logic before creating
    return this.repository.create(data);
  }
} 