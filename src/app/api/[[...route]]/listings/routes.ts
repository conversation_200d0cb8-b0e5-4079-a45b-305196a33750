import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { CreateListingUseCase } from './use-cases/create-listing.use-case';
import { GetListingUseCase } from './use-cases/get-listing.use-case';
import { ListListingsUseCase } from './use-cases/list-listings.use-case';
import { UpdateListingUseCase } from './use-cases/update-listing.use-case';
import { DeleteListingUseCase } from './use-cases/delete-listing.use-case';
import { CreateListingSchema } from './dto/create-listing.dto';
import { UpdateListingSchema } from './dto/update-listing.dto';
import { ListingQuerySchema } from './dto/listing-query.dto';
import { ListingParamsSchema } from './dto/listing-params.dto';
import { DrizzleListingRepository } from './repository/drizzle-listing.repository';

const repository = new DrizzleListingRepository();

// Use method chaining for proper type inference
const listingRoutes = new Hono()
  // GET /listings - List all listings with optional filtering
  .get(
    '/',
    zValidator('query', ListingQuerySchema),
    async (c) => {
      const filters = c.req.valid('query');
      const listListingsUseCase = new ListListingsUseCase(repository);
      const listings = await listListingsUseCase.execute(filters);
      
      return c.json(listings);
    }
  )
  // GET /listings/:id - Get a specific listing
  .get(
    '/:id',
    zValidator('param', ListingParamsSchema),
    async (c) => {
      const { id } = c.req.valid('param');
      const getListingUseCase = new GetListingUseCase(repository);
      const listing = await getListingUseCase.execute(id);
      
      if (!listing) {
        return c.json({ error: 'Listing not found' }, 404);
      }
      
      return c.json(listing);
    }
  )
  // POST /listings - Create a new listing
  .post(
    '/',
    zValidator('json', CreateListingSchema),
    async (c) => {
      const data = c.req.valid('json');
      const createListingUseCase = new CreateListingUseCase(repository);
      
      try {
        const newListing = await createListingUseCase.execute(data);
        return c.json(newListing, 201);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (_error) {
        return c.json({ error: 'Failed to create listing' }, 500);
      }
    }
  )
  // PUT /listings/:id - Update a listing
  .put(
    '/:id',
    zValidator('param', ListingParamsSchema),
    zValidator('json', UpdateListingSchema),
    async (c) => {
      const { id } = c.req.valid('param');
      const data = c.req.valid('json');
      const updateListingUseCase = new UpdateListingUseCase(repository);
      
      const updatedListing = await updateListingUseCase.execute(id, data);
      
      if (!updatedListing) {
        return c.json({ error: 'Listing not found' }, 404);
      }
      
      return c.json(updatedListing);
    }
  )
  // DELETE /listings/:id - Delete a listing
  .delete(
    '/:id',
    zValidator('param', ListingParamsSchema),
    async (c) => {
      const { id } = c.req.valid('param');
      const deleteListingUseCase = new DeleteListingUseCase(repository);
      const result = await deleteListingUseCase.execute(id);
      
      if (!result) {
        return c.json({ error: 'Listing not found or could not be deleted' }, 404);
      }
      
      return c.json({ success: true }, 200);
    }
  );

export { listingRoutes }; 