import { z } from 'zod';

export const ListingQuerySchema = z.object({
  minPrice: z.string().optional().transform(val => val ? Number(val) : undefined).pipe(z.number().positive().optional()),
  maxPrice: z.string().optional().transform(val => val ? Number(val) : undefined).pipe(z.number().positive().optional()),
  status: z.enum(['active', 'pending', 'closed']).optional(),
  title: z.string().optional(),
  page: z.string().optional().transform(val => val ? Number(val) : 1).pipe(z.number().int().min(1).default(1)),
  limit: z.string().optional().transform(val => val ? Number(val) : 10).pipe(z.number().int().min(1).max(100).default(10)),
});

export type ListingQueryDTO = z.infer<typeof ListingQuerySchema>; 