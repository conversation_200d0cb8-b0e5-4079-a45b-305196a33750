import { z } from 'zod';

export const FilterListingSchema = z.object({
  minPrice: z.number().positive().optional(),
  maxPrice: z.number().positive().optional(),
  status: z.enum(['active', 'pending', 'closed']).optional(),
  title: z.string().optional(),
  page: z.number().int().min(1).optional().default(1),
  limit: z.number().int().min(1).max(100).optional().default(10),
});

export type FilterListingDTO = z.infer<typeof FilterListingSchema>; 