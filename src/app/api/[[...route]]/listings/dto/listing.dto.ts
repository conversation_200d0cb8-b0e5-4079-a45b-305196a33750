import { z } from 'zod';
import { PropertySchema } from '../../properties/dto/property.dto';

export const ListingSchema = z.object({
  id: z.number(),
  createdAt: z.date(),
  updatedAt: z.date(),
  title: z.string(),
  description: z.string().nullable().optional(),
  price: z.number().positive(),
  status: z.enum(['active', 'pending', 'closed']),
  properties: z.array(PropertySchema).optional(),
});

export type ListingDTO = z.infer<typeof ListingSchema>; 