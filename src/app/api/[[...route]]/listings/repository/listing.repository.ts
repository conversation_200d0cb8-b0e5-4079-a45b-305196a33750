import type { CreateListingDTO } from '../dto/create-listing.dto';
import type { FilterListingDTO } from '../dto/filter-listing.dto';
import type { ListingDTO } from '../dto/listing.dto';
import type { UpdateListingDTO } from '../dto/update-listing.dto';

export interface ListingRepository {
  findAll(filters?: FilterListingDTO): Promise<ListingDTO[]>;
  findById(id: number): Promise<ListingDTO | null>;
  create(data: CreateListingDTO): Promise<ListingDTO>;
  update(id: number, data: UpdateListingDTO): Promise<ListingDTO | null>;
  delete(id: number): Promise<boolean>;
} 