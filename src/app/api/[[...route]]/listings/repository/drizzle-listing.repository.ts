import { eq, and, gte, lte, desc } from 'drizzle-orm';
import type { CreateListingDTO } from '../dto/create-listing.dto';
import type { FilterListingDTO } from '../dto/filter-listing.dto';
import type { ListingDTO } from '../dto/listing.dto';
import type { UpdateListingDTO } from '../dto/update-listing.dto';
import type { ListingRepository } from './listing.repository';
import { db, listings, properties, type Listing, type Property } from '@/lib/db';
import type { PropertyDTO } from '../../properties/dto/property.dto';

function mapPropertyToPropertyDTO(property: Property): PropertyDTO {
  return {
    id: property.id,
    createdAt: property.createdAt,
    updatedAt: property.updatedAt,
    title: property.title,
    description: property.description,
    address: property.address,
    price: Number(property.price),
    bedrooms: property.bedrooms || 0,
    bathrooms: Number(property.bathrooms) || 0,
    size: Number(property.size),
    status: property.status as PropertyDTO['status'],
  };
}

export class DrizzleListingRepository implements ListingRepository {
  private _mapStatusToListing(status: ListingDTO['status']): 'pending' | 'sold' | 'active' | 'withdrawn' {
    return status === 'closed' ? 'sold' : status as 'pending' | 'active' | 'withdrawn';
  }

  private _mapToListingDTO(listing: Listing, property?: Property): ListingDTO {
    return {
      id: listing.id,
      createdAt: listing.createdAt,
      updatedAt: listing.updatedAt,
      title: `Listing ${listing.id}`,
      description: `${listing.listingType} listing`,
      price: Number(listing.price),
      status: listing.status === 'sold' ? 'closed' : listing.status as ListingDTO['status'],
      properties: property ? [mapPropertyToPropertyDTO(property)] : [],
    };
  }

  async findAll(filters?: FilterListingDTO): Promise<ListingDTO[]> {
    const { minPrice, maxPrice, status, page = 1, limit = 10 } = filters || {};
    
    const conditions = [];
    if (minPrice) conditions.push(gte(listings.price, minPrice.toString()));
    if (maxPrice) conditions.push(lte(listings.price, maxPrice.toString()));
    if (status) conditions.push(eq(listings.status, this._mapStatusToListing(status)));

    const result = await db
      .select({ listing: listings, property: properties })
      .from(listings)
      .innerJoin(properties, eq(listings.propertyId, properties.id))
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .limit(limit)
      .offset((page - 1) * limit)
      .orderBy(desc(listings.createdAt));

    return result.map(({ listing, property }) => this._mapToListingDTO(listing, property));
  }

  async findById(id: number): Promise<ListingDTO | null> {
    const result = await db
      .select({ listing: listings, property: properties })
      .from(listings)
      .innerJoin(properties, eq(listings.propertyId, properties.id))
      .where(eq(listings.id, id));
    
    if (!result[0]) return null;

    return this._mapToListingDTO(result[0].listing, result[0].property);
  }

  async create(data: CreateListingDTO): Promise<ListingDTO> {
    const result = await db.insert(listings).values({
      propertyId: 1,
      listingType: 'sale',
      price: data.price.toString(),
      status: data.status === 'closed' ? 'sold' : (data.status as 'active' | 'pending' | 'withdrawn'),
      updatedAt: new Date(),
    }).returning();

    return this._mapToListingDTO(result[0], undefined);
  }

  async update(id: number, data: UpdateListingDTO): Promise<ListingDTO | null> {
    try {
      const result = await db
        .update(listings)
        .set({
          price: data.price?.toString(),
          status: data.status ? this._mapStatusToListing(data.status) : undefined,
          updatedAt: new Date(),
        })
        .where(eq(listings.id, id))
        .returning();

      if (!result[0]) return null;

      const propertyResult = await db
        .select()
        .from(properties)
        .where(eq(properties.id, result[0].propertyId))
        .limit(1);

      return this._mapToListingDTO(result[0], propertyResult[0]);
    } catch {
      return null;
    }
  }

  async delete(id: number): Promise<boolean> {
    try {
      await db.delete(listings).where(eq(listings.id, id));
      return true;
    } catch {
      return false;
    }
  }
} 