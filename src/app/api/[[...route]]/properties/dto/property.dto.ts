import { z } from 'zod';

export const PropertySchema = z.object({
  id: z.number(),
  createdAt: z.date(),
  updatedAt: z.date(),
  title: z.string(),
  description: z.string().nullable().optional(),
  address: z.string(),
  price: z.number().positive(),
  bedrooms: z.number().int().nonnegative(),
  bathrooms: z.number().nonnegative(),
  size: z.number().positive(),
  status: z.enum(['available', 'pending', 'sold']),
});

export type PropertyDTO = z.infer<typeof PropertySchema>;