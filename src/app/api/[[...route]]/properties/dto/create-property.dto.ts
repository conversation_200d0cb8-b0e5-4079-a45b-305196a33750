import { z } from 'zod';

export const CreatePropertySchema = z.object({
  title: z.string().min(3).max(100),
  description: z.string().max(1000).optional(),
  address: z.string().min(5).max(200),
  price: z.number().positive(),
  bedrooms: z.number().int().nonnegative(),
  bathrooms: z.number().nonnegative(),
  size: z.number().positive(),
  status: z.enum(['available', 'pending', 'sold']).default('available'),
});

export type CreatePropertyDTO = z.infer<typeof CreatePropertySchema>;