import { z } from 'zod';

export const UpdatePropertySchema = z.object({
  title: z.string().min(3).max(100).optional(),
  description: z.string().max(1000).optional(),
  address: z.string().min(5).max(200).optional(),
  price: z.number().positive().optional(),
  bedrooms: z.number().int().nonnegative().optional(),
  bathrooms: z.number().nonnegative().optional(),
  size: z.number().positive().optional(),
  status: z.enum(['available', 'pending', 'sold']).optional(),
});

export type UpdatePropertyDTO = z.infer<typeof UpdatePropertySchema>;