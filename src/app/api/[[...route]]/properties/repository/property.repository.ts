import type { CreatePropertyDTO } from '../dto/create-property.dto';
import type { PropertyDTO } from '../dto/property.dto';
import type { UpdatePropertyDTO } from '../dto/update-property.dto';

export interface PropertyRepository {
  findAll(): Promise<PropertyDTO[]>;
  findById(id: number): Promise<PropertyDTO | null>;
  create(data: CreatePropertyDTO): Promise<PropertyDTO>;
  update(id: number, data: UpdatePropertyDTO): Promise<PropertyDTO | null>;
  delete(id: number): Promise<boolean>;
}