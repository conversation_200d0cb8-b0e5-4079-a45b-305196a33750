import { eq } from 'drizzle-orm';
import type { CreatePropertyDTO } from '../dto/create-property.dto';
import type { PropertyDTO } from '../dto/property.dto';
import type { UpdatePropertyDTO } from '../dto/update-property.dto';
import type { PropertyRepository } from './property.repository';
import { db, properties, type Property } from '@/lib/db';

export class DrizzlePropertyRepository implements PropertyRepository {
  private _mapToPropertyDTO(property: Property): PropertyDTO {
    return {
      id: property.id,
      createdAt: property.createdAt,
      updatedAt: property.updatedAt,
      title: property.title,
      description: property.description,
      address: property.address,
      price: Number(property.price),
      bedrooms: property.bedrooms || 0,
      bathrooms: Number(property.bathrooms) || 0,
      size: Number(property.size),
      status: property.status as PropertyDTO['status'],
    };
  }

  async findAll(): Promise<PropertyDTO[]> {
    const result = await db.select().from(properties);
    return result.map(this._mapToPropertyDTO);
  }

  async findById(id: number): Promise<PropertyDTO | null> {
    const result = await db.select().from(properties).where(eq(properties.id, id));
    return result[0] ? this._mapToPropertyDTO(result[0]) : null;
  }

  async create(data: CreatePropertyDTO): Promise<PropertyDTO> {
    const result = await db.insert(properties).values({
      title: data.title,
      description: data.description,
      propertyType: 'house',
      address: data.address,
      city: 'Unknown',
      price: data.price.toString(),
      bedrooms: data.bedrooms,
      bathrooms: data.bathrooms.toString(),
      size: data.size.toString(),
      status: data.status,
      updatedAt: new Date(),
    }).returning();
    return this._mapToPropertyDTO(result[0]);
  }

  async update(id: number, data: UpdatePropertyDTO): Promise<PropertyDTO | null> {
    try {
      const result = await db
        .update(properties)
        .set({
          title: data.title,
          description: data.description,
          address: data.address,
          price: data.price?.toString(),
          bedrooms: data.bedrooms,
          bathrooms: data.bathrooms?.toString(),
          size: data.size?.toString(),
          status: data.status,
          updatedAt: new Date(),
        })
        .where(eq(properties.id, id))
        .returning();
      return result[0] ? this._mapToPropertyDTO(result[0]) : null;
    } catch {
      return null;
    }
  }

  async delete(id: number): Promise<boolean> {
    try {
      await db.delete(properties).where(eq(properties.id, id));
      return true;
    } catch {
      return false;
    }
  }
} 