import type { CreatePropertyDTO } from '../dto/create-property.dto';
import type { PropertyDTO } from '../dto/property.dto';
import type { PropertyRepository } from '../repository/property.repository';

export class CreatePropertyUseCase {
  constructor(private repository: PropertyRepository) {}

  async execute(data: CreatePropertyDTO): Promise<PropertyDTO> {
    // Here you can add additional business logic before creating
    return this.repository.create(data);
  }
}