import type { PropertyDTO } from '../dto/property.dto';
import type { UpdatePropertyDTO } from '../dto/update-property.dto';
import type { PropertyRepository } from '../repository/property.repository';

export class UpdatePropertyUseCase {
  constructor(private repository: PropertyRepository) {}

  async execute(id: number, data: UpdatePropertyDTO): Promise<PropertyDTO | null> {
    return this.repository.update(id, data);
  }
}