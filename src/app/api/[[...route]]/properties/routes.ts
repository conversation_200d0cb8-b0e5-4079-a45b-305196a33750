import { <PERSON>o } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { DrizzlePropertyRepository } from './repository/drizzle-property.repository';
import { CreatePropertyUseCase } from './use-cases/create-property.use-case';
import { GetPropertyUseCase } from './use-cases/get-property.use-case';
import { ListPropertiesUseCase } from './use-cases/list-properties.use-case';
import { UpdatePropertyUseCase } from './use-cases/update-property.use-case';
import { DeletePropertyUseCase } from './use-cases/delete-property.use-case';
import { CreatePropertySchema } from './dto/create-property.dto';
import { UpdatePropertySchema } from './dto/update-property.dto';

const repository = new DrizzlePropertyRepository();

// Use method chaining for proper type inference
const propertyRoutes = new Hono()
  // GET /properties - List all properties
  .get('/', async (c) => {
    const listPropertiesUseCase = new ListPropertiesUseCase(repository);
    const properties = await listPropertiesUseCase.execute();
    return c.json(properties);
  })
  // GET /properties/:id - Get a specific property
  .get('/:id', async (c) => {
    const id = Number.parseInt(c.req.param('id'));
    if (Number.isNaN(id)) {
      return c.json({ error: 'Invalid ID format' }, 400);
    }

    const getPropertyUseCase = new GetPropertyUseCase(repository);
    const property = await getPropertyUseCase.execute(id);
    
    if (!property) {
      return c.json({ error: 'Property not found' }, 404);
    }
    
    return c.json(property);
  })
  // POST /properties - Create a new property
  .post(
    '/',
    zValidator('json', CreatePropertySchema),
    async (c) => {
      const data = c.req.valid('json');
      const createPropertyUseCase = new CreatePropertyUseCase(repository);
      
      try {
        const newProperty = await createPropertyUseCase.execute(data);
        return c.json(newProperty, 201);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (_error) {
        return c.json({ error: 'Failed to create property' }, 500);
      }
    }
  )
  // PUT /properties/:id - Update a property
  .put(
    '/:id',
    zValidator('json', UpdatePropertySchema),
    async (c) => {
      const id = Number.parseInt(c.req.param('id'));
      if (Number.isNaN(id)) {
        return c.json({ error: 'Invalid ID format' }, 400);
      }

      const data = c.req.valid('json');
      const updatePropertyUseCase = new UpdatePropertyUseCase(repository);
      
      const updatedProperty = await updatePropertyUseCase.execute(id, data);
      
      if (!updatedProperty) {
        return c.json({ error: 'Property not found' }, 404);
      }
      
      return c.json(updatedProperty);
    }
  )
  // DELETE /properties/:id - Delete a property
  .delete('/:id', async (c) => {
    const id = Number.parseInt(c.req.param('id'));
    if (Number.isNaN(id)) {
      return c.json({ error: 'Invalid ID format' }, 400);
    }

    const deletePropertyUseCase = new DeletePropertyUseCase(repository);
    const result = await deletePropertyUseCase.execute(id);
    
    if (!result) {
      return c.json({ error: 'Property not found or could not be deleted' }, 404);
    }
    
    return c.json({ success: true }, 200);
  });

export { propertyRoutes };