import React from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Link from 'next/link'
import { 
  ArrowLeft, 
  Upload, 
  DollarSign,
  Home,
  Bed,
  Bath,
  Square,
  Check,
  Camera
} from 'lucide-react'

export const runtime = 'edge'

// Property types
const propertyTypes = ['Apartment', 'House', 'Condo', 'Townhouse', 'Studio', 'Loft']

// Common features
const commonFeatures = [
  'In-unit laundry',
  'Hardwood floors',
  'Stainless steel appliances',
  'Central air conditioning',
  'Balcony',
  'Walk-in closets',
  'Dishwasher',
  'Granite countertops',
  'Fireplace',
  'Updated kitchen',
  'Updated bathroom',
  'Storage space'
]

// Common amenities
const commonAmenities = [
  'Fitness center',
  'Swimming pool',
  'Parking garage',
  'Concierge service',
  'Rooftop terrace',
  'Business center',
  'Pet-friendly',
  'High-speed internet',
  'Laundry facilities',
  'Security system',
  'Elevator',
  'Garden/Courtyard'
]

export default function CreateListingPage() {
  const [selectedFeatures, setSelectedFeatures] = React.useState<string[]>([])
  const [selectedAmenities, setSelectedAmenities] = React.useState<string[]>([])

  const toggleFeature = (feature: string) => {
    setSelectedFeatures(prev => 
      prev.includes(feature) 
        ? prev.filter(f => f !== feature)
        : [...prev, feature]
    )
  }

  const toggleAmenity = (amenity: string) => {
    setSelectedAmenities(prev => 
      prev.includes(amenity) 
        ? prev.filter(a => a !== amenity)
        : [...prev, amenity]
    )
  }

  return (
    <main className="min-h-screen bg-slate-50">
      <div className="container mx-auto py-8 px-4">
        {/* Header */}
        <div className="mb-8">
          <Link href="/profile">
            <Button variant="ghost" className="flex items-center gap-2 mb-4">
              <ArrowLeft className="h-4 w-4" />
              Back to Profile
            </Button>
          </Link>
          <h1 className="text-3xl font-bold text-slate-900 mb-2">Create New Listing</h1>
          <p className="text-slate-600">Fill out the form below to list your property</p>
        </div>

        <form className="space-y-8">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Home className="h-5 w-5" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Property Title *</label>
                  <input
                    type="text"
                    placeholder="e.g., Modern Downtown Apartment"
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Property Type *</label>
                  <select 
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Select property type</option>
                    {propertyTypes.map(type => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Address *</label>
                <input
                  type="text"
                  placeholder="e.g., 123 Main Street, City, State, ZIP"
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Monthly Rent *</label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                    <input
                      type="number"
                      placeholder="2500"
                      className="w-full pl-9 pr-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Square Footage</label>
                  <div className="relative">
                    <Square className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                    <input
                      type="number"
                      placeholder="1200"
                      className="w-full pl-9 pr-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Bedrooms *</label>
                  <div className="relative">
                    <Bed className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                    <input
                      type="number"
                      min="0"
                      placeholder="2"
                      className="w-full pl-9 pr-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Bathrooms *</label>
                  <div className="relative">
                    <Bath className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                    <input
                      type="number"
                      min="0"
                      step="0.5"
                      placeholder="2"
                      className="w-full pl-9 pr-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Description */}
          <Card>
            <CardHeader>
              <CardTitle>Description</CardTitle>
            </CardHeader>
            <CardContent>
              <div>
                <label className="block text-sm font-medium mb-1">Property Description</label>
                <textarea
                  rows={5}
                  placeholder="Describe your property, highlighting its best features and what makes it special..."
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </CardContent>
          </Card>

          {/* Images */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Camera className="h-5 w-5" />
                Property Images
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border-2 border-dashed border-slate-300 rounded-lg p-8 text-center">
                <Upload className="h-12 w-12 mx-auto mb-4 text-slate-400" />
                <h3 className="text-lg font-medium mb-2">Upload Property Images</h3>
                <p className="text-slate-600 mb-4">
                  Drag and drop images here, or click to browse
                </p>
                <Button variant="outline">
                  Choose Files
                </Button>
              </div>
              <p className="text-sm text-slate-500">
                Upload up to 10 high-quality images. First image will be used as the main photo.
              </p>
            </CardContent>
          </Card>

          {/* Features */}
          <Card>
            <CardHeader>
              <CardTitle>Property Features</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-slate-600">Select all features that apply to your property:</p>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {commonFeatures.map(feature => (
                  <button
                    key={feature}
                    type="button"
                    onClick={() => toggleFeature(feature)}
                    className={`flex items-center justify-between p-3 border rounded-lg text-left transition-colors ${
                      selectedFeatures.includes(feature)
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-slate-300 hover:border-slate-400'
                    }`}
                  >
                    <span className="text-sm">{feature}</span>
                    {selectedFeatures.includes(feature) && (
                      <Check className="h-4 w-4 text-blue-600" />
                    )}
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Amenities */}
          <Card>
            <CardHeader>
              <CardTitle>Building Amenities</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-slate-600">Select all building amenities available:</p>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {commonAmenities.map(amenity => (
                  <button
                    key={amenity}
                    type="button"
                    onClick={() => toggleAmenity(amenity)}
                    className={`flex items-center justify-between p-3 border rounded-lg text-left transition-colors ${
                      selectedAmenities.includes(amenity)
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-slate-300 hover:border-slate-400'
                    }`}
                  >
                    <span className="text-sm">{amenity}</span>
                    {selectedAmenities.includes(amenity) && (
                      <Check className="h-4 w-4 text-blue-600" />
                    )}
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Contact Name *</label>
                  <input
                    type="text"
                    placeholder="Your name"
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Phone Number *</label>
                  <input
                    type="tel"
                    placeholder="(*************"
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Email Address *</label>
                  <input
                    type="email"
                    placeholder="<EMAIL>"
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Submit */}
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col sm:flex-row gap-4 justify-end">
                <Button variant="outline" className="px-8">
                  Save as Draft
                </Button>
                <Button type="submit" className="px-8">
                  Publish Listing
                </Button>
              </div>
            </CardContent>
          </Card>
        </form>
      </div>
    </main>
  )
} 