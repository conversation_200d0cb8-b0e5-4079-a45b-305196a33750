import React from 'react'
import { Suspense } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import Link from 'next/link'
import { Search, Filter, MapPin, Bed, Bath, Square, Heart } from 'lucide-react'

export const runtime = 'edge'

// Mock data - replace with actual API calls
const mockListings = [
  {
    id: '1',
    title: 'Modern Downtown Apartment',
    price: '$2,500/month',
    location: 'Downtown, City Center',
    bedrooms: 2,
    bathrooms: 2,
    area: '1,200 sq ft',
    image: '/placeholder-property.jpg',
    type: 'Apartment',
    featured: true
  },
  {
    id: '2',
    title: 'Spacious Family Home',
    price: '$4,200/month',
    location: 'Suburbs, Green Valley',
    bedrooms: 4,
    bathrooms: 3,
    area: '2,800 sq ft',
    image: '/placeholder-property.jpg',
    type: 'House',
    featured: false
  },
  // Add more mock listings as needed
]

function ListingCard({ listing }: { listing: typeof mockListings[0] }) {
  return (
    <Card className="group hover:shadow-lg transition-shadow duration-300">
      <div className="relative overflow-hidden rounded-t-lg">
        <div className="aspect-video bg-slate-200 flex items-center justify-center">
          <span className="text-slate-500">Property Image</span>
        </div>
        {listing.featured && (
          <Badge className="absolute top-3 left-3 bg-blue-600">Featured</Badge>
        )}
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-3 right-3 bg-white/80 hover:bg-white"
        >
          <Heart className="h-4 w-4" />
        </Button>
      </div>
      
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg group-hover:text-blue-600 transition-colors">
            <Link href={`/listings/${listing.id}`}>
              {listing.title}
            </Link>
          </CardTitle>
          <Badge variant="outline">{listing.type}</Badge>
        </div>
        <p className="text-2xl font-bold text-blue-600">{listing.price}</p>
      </CardHeader>
      
      <CardContent className="space-y-3">
        <div className="flex items-center text-slate-600">
          <MapPin className="h-4 w-4 mr-1" />
          <span className="text-sm">{listing.location}</span>
        </div>
        
        <div className="flex justify-between text-sm text-slate-600">
          <div className="flex items-center">
            <Bed className="h-4 w-4 mr-1" />
            <span>{listing.bedrooms} bed</span>
          </div>
          <div className="flex items-center">
            <Bath className="h-4 w-4 mr-1" />
            <span>{listing.bathrooms} bath</span>
          </div>
          <div className="flex items-center">
            <Square className="h-4 w-4 mr-1" />
            <span>{listing.area}</span>
          </div>
        </div>
        
        <Link href={`/listings/${listing.id}`}>
          <Button className="w-full">View Details</Button>
        </Link>
      </CardContent>
    </Card>
  )
}

export default function ListingsPage() {
  return (
    <main className="min-h-screen bg-slate-50">
      <div className="container mx-auto py-8 px-4">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-slate-900 mb-2">
            Property Listings
          </h1>
          <p className="text-slate-600">
            Find your perfect home from our extensive collection of properties
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg p-6 mb-8 shadow-sm">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-3 h-5 w-5 text-slate-400" />
              <input
                type="text"
                placeholder="Search by location, property type, or keyword..."
                className="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="flex gap-2">
              <Button variant="outline" className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Filters
              </Button>
              <Button>Search</Button>
            </div>
          </div>
        </div>

        {/* Results Info */}
        <div className="flex justify-between items-center mb-6">
          <p className="text-slate-600">
            Showing {mockListings.length} properties
          </p>
          <div className="flex gap-2">
            <select className="px-3 py-2 border border-slate-300 rounded-lg">
              <option>Sort by: Latest</option>
              <option>Price: Low to High</option>
              <option>Price: High to Low</option>
              <option>Area: Largest First</option>
            </select>
          </div>
        </div>

        {/* Listings Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <Suspense fallback={<div>Loading listings...</div>}>
            {mockListings.map((listing) => (
              <ListingCard key={listing.id} listing={listing} />
            ))}
          </Suspense>
        </div>

        {/* Pagination */}
        <div className="flex justify-center">
          <div className="flex gap-2">
            <Button variant="outline">Previous</Button>
            <Button variant="outline">1</Button>
            <Button>2</Button>
            <Button variant="outline">3</Button>
            <Button variant="outline">Next</Button>
          </div>
        </div>
      </div>
    </main>
  )
} 