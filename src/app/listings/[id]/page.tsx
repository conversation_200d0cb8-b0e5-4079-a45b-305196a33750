import React from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import Link from 'next/link'
import { 
  ArrowLeft, 
  MapPin, 
  Bed, 
  Bath, 
  Square, 
  Heart, 
  Share2, 
  Phone, 
  Mail,
  Check,
  Calendar,
  Car,
  Wifi,
  Dumbbell
} from 'lucide-react'

export const runtime = 'edge'

// Mock data - replace with actual API call
const mockListing = {
  id: '1',
  title: 'Modern Downtown Apartment',
  price: '$2,500/month',
  location: 'Downtown, City Center',
  fullAddress: '123 Main Street, Downtown, City Center, State 12345',
  bedrooms: 2,
  bathrooms: 2,
  area: '1,200 sq ft',
  type: 'Apartment',
  featured: true,
  description: 'This stunning modern apartment offers the perfect blend of luxury and convenience in the heart of downtown. With floor-to-ceiling windows, high-end finishes, and an open-concept layout, this property provides an exceptional living experience.',
  features: [
    'In-unit laundry',
    'Hardwood floors',
    'Stainless steel appliances',
    'Central air conditioning',
    'Balcony with city views',
    'Walk-in closets',
    'Dishwasher',
    'Granite countertops'
  ],
  amenities: [
    'Fitness center',
    'Rooftop terrace',
    'Concierge service',
    'Parking garage',
    'Pet-friendly',
    'High-speed internet',
    'Swimming pool',
    'Business center'
  ],
  agent: {
    name: 'Sarah Johnson',
    phone: '(*************',
    email: '<EMAIL>',
    company: 'Premium Properties'
  },
  images: [
    '/placeholder-property.jpg',
    '/placeholder-property.jpg',
    '/placeholder-property.jpg',
    '/placeholder-property.jpg'
  ]
}

export default async function ListingDetailPage({ 
  params 
}: { 
  params: Promise<{ id: string }> 
}) {
  const { id } = await params
  
  // In a real app, fetch the listing data based on the id
  // const listing = await fetchListing(id)
  const listing = mockListing

  return (
    <main className="min-h-screen bg-slate-50">
      <div className="container mx-auto py-8 px-4">
        {/* Back Button */}
        <div className="mb-6">
          <Link href="/listings">
            <Button variant="ghost" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Listings
            </Button>
          </Link>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Image Gallery */}
            <Card>
              <CardContent className="p-0">
                <div className="aspect-video bg-slate-200 rounded-lg flex items-center justify-center">
                  <span className="text-slate-500">Property Image Gallery</span>
                </div>
                <div className="grid grid-cols-4 gap-2 p-4">
                  {listing.images.slice(1).map((_, index) => (
                    <div key={index} className="aspect-video bg-slate-200 rounded flex items-center justify-center">
                      <span className="text-xs text-slate-500">Photo {index + 2}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Property Details */}
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-2xl mb-2">{listing.title}</CardTitle>
                    <div className="flex items-center text-slate-600 mb-2">
                      <MapPin className="h-4 w-4 mr-1" />
                      <span>{listing.fullAddress}</span>
                    </div>
                    <p className="text-3xl font-bold text-blue-600">{listing.price}</p>
                  </div>
                  <div className="flex gap-2">
                    {listing.featured && (
                      <Badge className="bg-blue-600">Featured</Badge>
                    )}
                    <Badge variant="outline">{listing.type}</Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Property Stats */}
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-slate-50 rounded-lg">
                    <Bed className="h-6 w-6 mx-auto mb-2 text-slate-600" />
                    <p className="text-2xl font-bold">{listing.bedrooms}</p>
                    <p className="text-sm text-slate-600">Bedrooms</p>
                  </div>
                  <div className="text-center p-4 bg-slate-50 rounded-lg">
                    <Bath className="h-6 w-6 mx-auto mb-2 text-slate-600" />
                    <p className="text-2xl font-bold">{listing.bathrooms}</p>
                    <p className="text-sm text-slate-600">Bathrooms</p>
                  </div>
                  <div className="text-center p-4 bg-slate-50 rounded-lg">
                    <Square className="h-6 w-6 mx-auto mb-2 text-slate-600" />
                    <p className="text-2xl font-bold">{listing.area}</p>
                    <p className="text-sm text-slate-600">Area</p>
                  </div>
                </div>

                <Separator />

                {/* Description */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Description</h3>
                  <p className="text-slate-700 leading-relaxed">{listing.description}</p>
                </div>

                <Separator />

                {/* Features */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Property Features</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {listing.features.map((feature, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <Check className="h-4 w-4 text-green-600" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator />

                {/* Amenities */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Building Amenities</h3>
                  <div className="grid grid-cols-2 gap-4">
                    {listing.amenities.map((amenity, index) => (
                      <div key={index} className="flex items-center gap-2">
                        {amenity.includes('Fitness') && <Dumbbell className="h-4 w-4 text-blue-600" />}
                        {amenity.includes('Parking') && <Car className="h-4 w-4 text-blue-600" />}
                        {amenity.includes('internet') && <Wifi className="h-4 w-4 text-blue-600" />}
                        {!amenity.includes('Fitness') && !amenity.includes('Parking') && !amenity.includes('internet') && 
                          <Check className="h-4 w-4 text-blue-600" />}
                        <span className="text-sm">{amenity}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Action Buttons */}
            <Card>
              <CardContent className="p-6 space-y-3">
                <Button className="w-full" size="lg">
                  <Calendar className="h-4 w-4 mr-2" />
                  Schedule Tour
                </Button>
                <Button variant="outline" className="w-full">
                  <Heart className="h-4 w-4 mr-2" />
                  Save Property
                </Button>
                <Button variant="outline" className="w-full">
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </Button>
              </CardContent>
            </Card>

            {/* Contact Agent */}
            <Card>
              <CardHeader>
                <CardTitle>Contact Agent</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold">{listing.agent.name}</h4>
                  <p className="text-sm text-slate-600">{listing.agent.company}</p>
                </div>
                
                <div className="space-y-2">
                  <Button variant="outline" className="w-full justify-start">
                    <Phone className="h-4 w-4 mr-2" />
                    {listing.agent.phone}
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Mail className="h-4 w-4 mr-2" />
                    Email Agent
                  </Button>
                </div>

                <Separator />

                <div className="space-y-3">
                  <input
                    type="text"
                    placeholder="Your Name"
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg"
                  />
                  <input
                    type="email"
                    placeholder="Your Email"
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg"
                  />
                  <textarea
                    placeholder="Message (optional)"
                    rows={3}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg"
                  />
                  <Button className="w-full">Send Message</Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </main>
  )
} 