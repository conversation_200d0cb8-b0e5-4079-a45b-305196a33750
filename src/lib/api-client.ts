import { hc } from 'hono/client'
import type { AppType } from '@/app/api/[[...route]]/route'

function getBaseURL() {
  if (typeof window !== 'undefined') {
    return ''
  }
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`
  }
  return 'http://localhost:3000'
}

// Create typed client
export const client = hc<AppType>(getBaseURL())

export type Client = typeof client

export const hcWithType = (...args: Parameters<typeof hc>): Client =>
  hc<AppType>(...args)