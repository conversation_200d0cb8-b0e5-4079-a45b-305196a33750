import {
  pgTable,
  serial,
  varchar,
  boolean,
  timestamp,
  integer,
  json,
  index,
  unique,
} from 'drizzle-orm/pg-core';
import { userTypeEnum, membershipRoleEnum, membershipStatusEnum } from './enums';

// Users table
export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  passwordHash: varchar('password_hash', { length: 255 }).notNull(),
  firstName: varchar('first_name', { length: 100 }).notNull(),
  lastName: varchar('last_name', { length: 100 }).notNull(),
  phone: varchar('phone', { length: 20 }),
  avatarUrl: varchar('avatar_url', { length: 500 }),
  userType: userTypeEnum('user_type').notNull().default('private'),
  isVerified: boolean('is_verified').notNull().default(false),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
}, (table) => ({
  emailIdx: index('users_email_idx').on(table.email),
  userTypeIdx: index('users_user_type_idx').on(table.userType),
}));

// Organization memberships table
export const organizationMemberships = pgTable('organization_memberships', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').notNull(),
  organizationId: integer('organization_id').notNull(),
  role: membershipRoleEnum('role').notNull(),
  status: membershipStatusEnum('status').notNull().default('pending'),
  permissions: json('permissions'),
  joinedAt: timestamp('joined_at').notNull().defaultNow(),
  leftAt: timestamp('left_at'),
}, (table) => ({
  userIdIdx: index('org_memberships_user_id_idx').on(table.userId),
  organizationIdIdx: index('org_memberships_organization_id_idx').on(table.organizationId),
  uniqueMembership: unique('unique_user_org_status').on(table.userId, table.organizationId, table.status),
}));

// Export types
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert; 