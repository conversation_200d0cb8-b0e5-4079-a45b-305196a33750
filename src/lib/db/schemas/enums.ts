import { pgEnum } from 'drizzle-orm/pg-core';

// User and organization enums
export const userTypeEnum = pgEnum('user_type', ['private', 'professional']);
export const organizationTypeEnum = pgEnum('organization_type', ['agency', 'developer', 'other']);
export const membershipRoleEnum = pgEnum('membership_role', ['admin', 'manager', 'agent', 'employee']);
export const membershipStatusEnum = pgEnum('membership_status', ['active', 'inactive', 'pending']);

// Property and listing enums
export const propertyTypeEnum = pgEnum('property_type', ['house', 'apartment', 'commercial', 'land', 'other']);
export const listingTypeEnum = pgEnum('listing_type', ['sale', 'rent', 'lease']);
export const listingStatusEnum = pgEnum('listing_status', ['active', 'pending', 'sold', 'withdrawn']);
export const listingActionEnum = pgEnum('listing_action', ['created', 'updated', 'status_changed', 'ownership_transferred']); 