import {
  pgTable,
  serial,
  varchar,
  text,
  boolean,
  timestamp,
  integer,
  json,
  unique,
} from 'drizzle-orm/pg-core';
import { organizationTypeEnum } from './enums';

// Organizations table
export const organizations = pgTable('organizations', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  organizationType: organizationTypeEnum('organization_type').notNull(),
  description: text('description'),
  website: varchar('website', { length: 500 }),
  logoUrl: varchar('logo_url', { length: 500 }),
  address: text('address'),
  phone: varchar('phone', { length: 20 }),
  licenseNumber: varchar('license_number', { length: 100 }),
  isActive: boolean('is_active').notNull().default(true),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Organization settings table
export const organizationSettings = pgTable('organization_settings', {
  id: serial('id').primaryKey(),
  organizationId: integer('organization_id').notNull(),
  settingKey: varchar('setting_key', { length: 100 }).notNull(),
  settingValue: json('setting_value').notNull(),
  updatedByUserId: integer('updated_by_user_id'),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
}, (table) => ({
  uniqueOrgSetting: unique('unique_org_setting').on(table.organizationId, table.settingKey),
}));

// Export types
export type Organization = typeof organizations.$inferSelect;
export type NewOrganization = typeof organizations.$inferInsert; 