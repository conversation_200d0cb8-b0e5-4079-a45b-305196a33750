import {
  pgTable,
  serial,
  text,
  boolean,
  timestamp,
  integer,
  decimal,
  json,
  index,
} from 'drizzle-orm/pg-core';
import { listingTypeEnum, listingStatusEnum, listingActionEnum } from './enums';

// Listings table
export const listings = pgTable('listings', {
  id: serial('id').primaryKey(),
  propertyId: integer('property_id').notNull(),
  listingType: listingTypeEnum('listing_type').notNull(),
  price: decimal('price', { precision: 12, scale: 2 }).notNull(),
  status: listingStatusEnum('status').notNull().default('active'),
  listedByUserId: integer('listed_by_user_id'),
  listedByOrganizationId: integer('listed_by_organization_id'),
  agentUserId: integer('agent_user_id'),
  listingDate: timestamp('listing_date').notNull().defaultNow(),
  expiryDate: timestamp('expiry_date'),
  commissionRate: decimal('commission_rate', { precision: 5, scale: 2 }),
  isFeatured: boolean('is_featured').notNull().default(false),
  viewsCount: integer('views_count').notNull().default(0),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
}, (table) => ({
  propertyIdIdx: index('listings_property_id_idx').on(table.propertyId),
  listedByUserIdIdx: index('listings_listed_by_user_id_idx').on(table.listedByUserId),
  listedByOrganizationIdIdx: index('listings_listed_by_organization_id_idx').on(table.listedByOrganizationId),
  agentUserIdIdx: index('listings_agent_user_id_idx').on(table.agentUserId),
  statusIdx: index('listings_status_idx').on(table.status),
}));

// Listing history table
export const listingHistory = pgTable('listing_history', {
  id: serial('id').primaryKey(),
  listingId: integer('listing_id').notNull(),
  action: listingActionEnum('action').notNull(),
  oldValues: json('old_values'),
  newValues: json('new_values'),
  performedByUserId: integer('performed_by_user_id'),
  performedAt: timestamp('performed_at').notNull().defaultNow(),
  notes: text('notes'),
});

// Export types
export type Listing = typeof listings.$inferSelect;
export type NewListing = typeof listings.$inferInsert; 