import {
  pgTable,
  serial,
  varchar,
  text,
  timestamp,
  integer,
  decimal,
  json,
  index,
} from 'drizzle-orm/pg-core';
import { propertyTypeEnum } from './enums';

// Properties table
export const properties = pgTable('properties', {
  id: serial('id').primaryKey(),
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description'),
  propertyType: propertyTypeEnum('property_type').notNull(),
  address: text('address').notNull(),
  city: varchar('city', { length: 100 }).notNull(),
  state: varchar('state', { length: 50 }),
  postalCode: varchar('postal_code', { length: 20 }),
  country: varchar('country', { length: 50 }).notNull().default('US'),
  latitude: decimal('latitude', { precision: 10, scale: 8 }),
  longitude: decimal('longitude', { precision: 11, scale: 8 }),
  bedrooms: integer('bedrooms'),
  bathrooms: decimal('bathrooms', { precision: 3, scale: 1 }),
  squareFeet: integer('square_feet'),
  lotSize: integer('lot_size'),
  yearBuilt: integer('year_built'),
  amenities: json('amenities'),
  images: json('images'),
  // Additional fields expected by the application
  price: decimal('price', { precision: 12, scale: 2 }).notNull(),
  size: decimal('size', { precision: 10, scale: 2 }).notNull(),
  status: varchar('status', { length: 20 }).notNull().default('available'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
}, (table) => ({
  cityStateIdx: index('properties_city_state_idx').on(table.city, table.state),
  propertyTypeIdx: index('properties_property_type_idx').on(table.propertyType),
  statusIdx: index('properties_status_idx').on(table.status),
}));

// Export types
export type Property = typeof properties.$inferSelect;
export type NewProperty = typeof properties.$inferInsert; 