import { relations } from 'drizzle-orm';
import { users, organizationMemberships } from './users';
import { organizations, organizationSettings } from './organizations';
import { properties } from './properties';
import { listings, listingHistory } from './listings';

export const usersRelations = relations(users, ({ many }) => ({
  organizationMemberships: many(organizationMemberships),
  listedProperties: many(listings, { relationName: 'listedByUser' }),
  agentListings: many(listings, { relationName: 'agentUser' }),
  listingHistoryActions: many(listingHistory),
  organizationSettings: many(organizationSettings),
}));

export const organizationsRelations = relations(organizations, ({ many }) => ({
  memberships: many(organizationMemberships),
  listings: many(listings),
  organizationSettings: many(organizationSettings),
}));

export const organizationMembershipsRelations = relations(organizationMemberships, ({ one }) => ({
  user: one(users, {
    fields: [organizationMemberships.userId],
    references: [users.id],
  }),
  organization: one(organizations, {
    fields: [organizationMemberships.organizationId],
    references: [organizations.id],
  }),
}));

export const organizationSettingsRelations = relations(organizationSettings, ({ one }) => ({
  organization: one(organizations, {
    fields: [organizationSettings.organizationId],
    references: [organizations.id],
  }),
  updatedBy: one(users, {
    fields: [organizationSettings.updatedByUserId],
    references: [users.id],
  }),
}));

export const propertiesRelations = relations(properties, ({ many }) => ({
  listings: many(listings),
}));

export const listingsRelations = relations(listings, ({ one, many }) => ({
  property: one(properties, {
    fields: [listings.propertyId],
    references: [properties.id],
  }),
  listedByUser: one(users, {
    fields: [listings.listedByUserId],
    references: [users.id],
    relationName: 'listedByUser',
  }),
  listedByOrganization: one(organizations, {
    fields: [listings.listedByOrganizationId],
    references: [organizations.id],
  }),
  agentUser: one(users, {
    fields: [listings.agentUserId],
    references: [users.id],
    relationName: 'agentUser',
  }),
  listingHistory: many(listingHistory),
}));

export const listingHistoryRelations = relations(listingHistory, ({ one }) => ({
  listing: one(listings, {
    fields: [listingHistory.listingId],
    references: [listings.id],
  }),
  performedByUser: one(users, {
    fields: [listingHistory.performedByUserId],
    references: [users.id],
  }),
})); 