# Database Schema Organization

This directory contains the modular database schema files organized by domain for better maintainability and clarity.

## File Structure

```
schemas/
├── index.ts          # Main export file - exports all schemas, relations, and types
├── enums.ts          # All PostgreSQL enums used across the application
├── users.ts          # User-related tables and types
├── organizations.ts  # Organization-related tables and types
├── properties.ts     # Property-related tables and types
├── listings.ts       # Listing-related tables and types
├── relations.ts      # All table relations and foreign key definitions
└── README.md         # This documentation file
```

## Domain Organization

### `enums.ts`
Contains all PostgreSQL enum definitions:
- User and organization enums (`userTypeEnum`, `organizationTypeEnum`, etc.)
- Property and listing enums (`propertyTypeEnum`, `listingTypeEnum`, etc.)

### `users.ts`
User domain tables:
- `users` - Core user information
- `organizationMemberships` - User-organization relationships

### `organizations.ts`
Organization domain tables:
- `organizations` - Organization details
- `organizationSettings` - Organization configuration

### `properties.ts`
Property domain tables:
- `properties` - Property information and details

### `listings.ts`
Listing domain tables:
- `listings` - Property listings
- `listingHistory` - Listing change history

### `relations.ts`
All table relationships and foreign key constraints using <PERSON><PERSON><PERSON>'s relations API.

## Usage

Import from the main schema file (maintains backward compatibility):
```typescript
import { users, properties, listings } from '@/lib/db/schema';
```

Or import directly from specific domain files:
```typescript
import { users } from '@/lib/db/schemas/users';
import { properties } from '@/lib/db/schemas/properties';
```

## Benefits

- **Modularity**: Each domain is self-contained
- **Maintainability**: Easier to locate and modify specific tables
- **Clarity**: Clear separation of concerns
- **Scalability**: Easy to add new domains without cluttering
- **Backward Compatibility**: Existing imports continue to work 