CREATE TYPE "public"."listing_action" AS ENUM('created', 'updated', 'status_changed', 'ownership_transferred');--> statement-breakpoint
CREATE TYPE "public"."listing_status" AS ENUM('active', 'pending', 'sold', 'withdrawn');--> statement-breakpoint
CREATE TYPE "public"."listing_type" AS ENUM('sale', 'rent', 'lease');--> statement-breakpoint
CREATE TYPE "public"."membership_role" AS ENUM('admin', 'manager', 'agent', 'employee');--> statement-breakpoint
CREATE TYPE "public"."membership_status" AS ENUM('active', 'inactive', 'pending');--> statement-breakpoint
CREATE TYPE "public"."organization_type" AS ENUM('agency', 'developer', 'other');--> statement-breakpoint
CREATE TYPE "public"."property_type" AS ENUM('house', 'apartment', 'commercial', 'land', 'other');--> statement-breakpoint
CREATE TYPE "public"."user_type" AS ENUM('private', 'professional');--> statement-breakpoint
CREATE TABLE "listing_history" (
	"id" serial PRIMARY KEY NOT NULL,
	"listing_id" integer NOT NULL,
	"action" "listing_action" NOT NULL,
	"old_values" json,
	"new_values" json,
	"performed_by_user_id" integer,
	"performed_at" timestamp DEFAULT now() NOT NULL,
	"notes" text
);
--> statement-breakpoint
CREATE TABLE "listings" (
	"id" serial PRIMARY KEY NOT NULL,
	"property_id" integer NOT NULL,
	"listing_type" "listing_type" NOT NULL,
	"price" numeric(12, 2) NOT NULL,
	"status" "listing_status" DEFAULT 'active' NOT NULL,
	"listed_by_user_id" integer,
	"listed_by_organization_id" integer,
	"agent_user_id" integer,
	"listing_date" timestamp DEFAULT now() NOT NULL,
	"expiry_date" timestamp,
	"commission_rate" numeric(5, 2),
	"is_featured" boolean DEFAULT false NOT NULL,
	"views_count" integer DEFAULT 0 NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "offer_properties" (
	"id" serial PRIMARY KEY NOT NULL,
	"offer_id" integer NOT NULL,
	"property_id" integer NOT NULL,
	CONSTRAINT "unique_offer_property" UNIQUE("offer_id","property_id")
);
--> statement-breakpoint
CREATE TABLE "offers" (
	"id" serial PRIMARY KEY NOT NULL,
	"title" varchar(255) NOT NULL,
	"description" text,
	"price" numeric(12, 2) NOT NULL,
	"status" varchar(20) DEFAULT 'active' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "organization_memberships" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"organization_id" integer NOT NULL,
	"role" "membership_role" NOT NULL,
	"status" "membership_status" DEFAULT 'pending' NOT NULL,
	"permissions" json,
	"joined_at" timestamp DEFAULT now() NOT NULL,
	"left_at" timestamp,
	CONSTRAINT "unique_user_org_status" UNIQUE("user_id","organization_id","status")
);
--> statement-breakpoint
CREATE TABLE "organization_settings" (
	"id" serial PRIMARY KEY NOT NULL,
	"organization_id" integer NOT NULL,
	"setting_key" varchar(100) NOT NULL,
	"setting_value" json NOT NULL,
	"updated_by_user_id" integer,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "unique_org_setting" UNIQUE("organization_id","setting_key")
);
--> statement-breakpoint
CREATE TABLE "organizations" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"organization_type" "organization_type" NOT NULL,
	"description" text,
	"website" varchar(500),
	"logo_url" varchar(500),
	"address" text,
	"phone" varchar(20),
	"license_number" varchar(100),
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "properties" (
	"id" serial PRIMARY KEY NOT NULL,
	"title" varchar(255) NOT NULL,
	"description" text,
	"property_type" "property_type" NOT NULL,
	"address" text NOT NULL,
	"city" varchar(100) NOT NULL,
	"state" varchar(50),
	"postal_code" varchar(20),
	"country" varchar(50) DEFAULT 'US' NOT NULL,
	"latitude" numeric(10, 8),
	"longitude" numeric(11, 8),
	"bedrooms" integer,
	"bathrooms" numeric(3, 1),
	"square_feet" integer,
	"lot_size" integer,
	"year_built" integer,
	"amenities" json,
	"images" json,
	"price" numeric(12, 2) NOT NULL,
	"size" numeric(10, 2) NOT NULL,
	"status" varchar(20) DEFAULT 'available' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" serial PRIMARY KEY NOT NULL,
	"email" varchar(255) NOT NULL,
	"password_hash" varchar(255) NOT NULL,
	"first_name" varchar(100) NOT NULL,
	"last_name" varchar(100) NOT NULL,
	"phone" varchar(20),
	"avatar_url" varchar(500),
	"user_type" "user_type" DEFAULT 'private' NOT NULL,
	"is_verified" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "users_email_unique" UNIQUE("email")
);
--> statement-breakpoint
CREATE INDEX "listings_property_id_idx" ON "listings" USING btree ("property_id");--> statement-breakpoint
CREATE INDEX "listings_listed_by_user_id_idx" ON "listings" USING btree ("listed_by_user_id");--> statement-breakpoint
CREATE INDEX "listings_listed_by_organization_id_idx" ON "listings" USING btree ("listed_by_organization_id");--> statement-breakpoint
CREATE INDEX "listings_agent_user_id_idx" ON "listings" USING btree ("agent_user_id");--> statement-breakpoint
CREATE INDEX "listings_status_idx" ON "listings" USING btree ("status");--> statement-breakpoint
CREATE INDEX "org_memberships_user_id_idx" ON "organization_memberships" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "org_memberships_organization_id_idx" ON "organization_memberships" USING btree ("organization_id");--> statement-breakpoint
CREATE INDEX "properties_city_state_idx" ON "properties" USING btree ("city","state");--> statement-breakpoint
CREATE INDEX "properties_property_type_idx" ON "properties" USING btree ("property_type");--> statement-breakpoint
CREATE INDEX "users_email_idx" ON "users" USING btree ("email");--> statement-breakpoint
CREATE INDEX "users_user_type_idx" ON "users" USING btree ("user_type");