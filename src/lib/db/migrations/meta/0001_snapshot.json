{"id": "351fdd06-5985-48a9-85c8-663a8840d5cf", "prevId": "ef834153-6438-470d-8f47-f1483d36b24f", "version": "7", "dialect": "postgresql", "tables": {"public.organization_memberships": {"name": "organization_memberships", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "integer", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "membership_role", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "membership_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "permissions": {"name": "permissions", "type": "json", "primaryKey": false, "notNull": false}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "left_at": {"name": "left_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"org_memberships_user_id_idx": {"name": "org_memberships_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "org_memberships_organization_id_idx": {"name": "org_memberships_organization_id_idx", "columns": [{"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_user_org_status": {"name": "unique_user_org_status", "nullsNotDistinct": false, "columns": ["user_id", "organization_id", "status"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password_hash": {"name": "password_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "user_type": {"name": "user_type", "type": "user_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'private'"}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_user_type_idx": {"name": "users_user_type_idx", "columns": [{"expression": "user_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organization_settings": {"name": "organization_settings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "integer", "primaryKey": false, "notNull": true}, "setting_key": {"name": "setting_key", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "setting_value": {"name": "setting_value", "type": "json", "primaryKey": false, "notNull": true}, "updated_by_user_id": {"name": "updated_by_user_id", "type": "integer", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_org_setting": {"name": "unique_org_setting", "nullsNotDistinct": false, "columns": ["organization_id", "setting_key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organizations": {"name": "organizations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "organization_type": {"name": "organization_type", "type": "organization_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "logo_url": {"name": "logo_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "license_number": {"name": "license_number", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.properties": {"name": "properties", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "property_type": {"name": "property_type", "type": "property_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "postal_code": {"name": "postal_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'US'"}, "latitude": {"name": "latitude", "type": "numeric(10, 8)", "primaryKey": false, "notNull": false}, "longitude": {"name": "longitude", "type": "numeric(11, 8)", "primaryKey": false, "notNull": false}, "bedrooms": {"name": "bedrooms", "type": "integer", "primaryKey": false, "notNull": false}, "bathrooms": {"name": "bathrooms", "type": "numeric(3, 1)", "primaryKey": false, "notNull": false}, "square_feet": {"name": "square_feet", "type": "integer", "primaryKey": false, "notNull": false}, "lot_size": {"name": "lot_size", "type": "integer", "primaryKey": false, "notNull": false}, "year_built": {"name": "year_built", "type": "integer", "primaryKey": false, "notNull": false}, "amenities": {"name": "amenities", "type": "json", "primaryKey": false, "notNull": false}, "images": {"name": "images", "type": "json", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "numeric(12, 2)", "primaryKey": false, "notNull": true}, "size": {"name": "size", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'available'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"properties_city_state_idx": {"name": "properties_city_state_idx", "columns": [{"expression": "city", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "state", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_property_type_idx": {"name": "properties_property_type_idx", "columns": [{"expression": "property_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_status_idx": {"name": "properties_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.listing_history": {"name": "listing_history", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "listing_id": {"name": "listing_id", "type": "integer", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "listing_action", "typeSchema": "public", "primaryKey": false, "notNull": true}, "old_values": {"name": "old_values", "type": "json", "primaryKey": false, "notNull": false}, "new_values": {"name": "new_values", "type": "json", "primaryKey": false, "notNull": false}, "performed_by_user_id": {"name": "performed_by_user_id", "type": "integer", "primaryKey": false, "notNull": false}, "performed_at": {"name": "performed_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.listings": {"name": "listings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "property_id": {"name": "property_id", "type": "integer", "primaryKey": false, "notNull": true}, "listing_type": {"name": "listing_type", "type": "listing_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "numeric(12, 2)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "listing_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "listed_by_user_id": {"name": "listed_by_user_id", "type": "integer", "primaryKey": false, "notNull": false}, "listed_by_organization_id": {"name": "listed_by_organization_id", "type": "integer", "primaryKey": false, "notNull": false}, "agent_user_id": {"name": "agent_user_id", "type": "integer", "primaryKey": false, "notNull": false}, "listing_date": {"name": "listing_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "expiry_date": {"name": "expiry_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "commission_rate": {"name": "commission_rate", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "is_featured": {"name": "is_featured", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "views_count": {"name": "views_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"listings_property_id_idx": {"name": "listings_property_id_idx", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "listings_listed_by_user_id_idx": {"name": "listings_listed_by_user_id_idx", "columns": [{"expression": "listed_by_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "listings_listed_by_organization_id_idx": {"name": "listings_listed_by_organization_id_idx", "columns": [{"expression": "listed_by_organization_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "listings_agent_user_id_idx": {"name": "listings_agent_user_id_idx", "columns": [{"expression": "agent_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "listings_status_idx": {"name": "listings_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.listing_action": {"name": "listing_action", "schema": "public", "values": ["created", "updated", "status_changed", "ownership_transferred"]}, "public.listing_status": {"name": "listing_status", "schema": "public", "values": ["active", "pending", "sold", "withdrawn"]}, "public.listing_type": {"name": "listing_type", "schema": "public", "values": ["sale", "rent", "lease"]}, "public.membership_role": {"name": "membership_role", "schema": "public", "values": ["admin", "manager", "agent", "employee"]}, "public.membership_status": {"name": "membership_status", "schema": "public", "values": ["active", "inactive", "pending"]}, "public.organization_type": {"name": "organization_type", "schema": "public", "values": ["agency", "developer", "other"]}, "public.property_type": {"name": "property_type", "schema": "public", "values": ["house", "apartment", "commercial", "land", "other"]}, "public.user_type": {"name": "user_type", "schema": "public", "values": ["private", "professional"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}