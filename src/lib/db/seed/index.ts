import 'dotenv/config';
import { db } from '../index';
import { properties, listings } from '../schemas';

const sampleProperties = [
  {
    title: 'Modern Downtown Condo',
    description: 'Beautiful 2-bedroom condo in the heart of downtown with city views and premium amenities.',
    propertyType: 'apartment' as const,
    address: '123 Main Street, Unit 15B',
    city: 'San Francisco',
    state: 'CA',
    postalCode: '94105',
    country: 'US',
    latitude: '37.7749',
    longitude: '-122.4194',
    bedrooms: 2,
    bathrooms: '2.0',
    squareFeet: 1200,
    yearBuilt: 2018,
    amenities: ['gym', 'rooftop terrace', 'concierge', 'parking'],
    images: [
      'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267',
      'https://images.unsplash.com/photo-1484154218962-a197022b5858'
    ],
    price: '850000',
    size: '1200.00',
    status: 'available'
  },
  {
    title: 'Suburban Family Home',
    description: 'Spacious 4-bedroom family home with large backyard and excellent school district.',
    propertyType: 'house' as const,
    address: '456 Oak Avenue',
    city: 'Palo Alto',
    state: 'CA',
    postalCode: '94301',
    country: 'US',
    latitude: '37.4419',
    longitude: '-122.1430',
    bedrooms: 4,
    bathrooms: '3.5',
    squareFeet: 2800,
    lotSize: 8000,
    yearBuilt: 2010,
    amenities: ['garage', 'garden', 'fireplace', 'walk-in closets'],
    images: [
      'https://images.unsplash.com/photo-1568605114967-8130f3a36994',
      'https://images.unsplash.com/photo-1570129477492-45c003edd2be'
    ],
    price: '1950000',
    size: '2800.00',
    status: 'available'
  },
  {
    title: 'Luxury Penthouse',
    description: 'Stunning penthouse with panoramic views, private elevator, and premium finishes throughout.',
    propertyType: 'apartment' as const,
    address: '789 Skyline Boulevard, Penthouse',
    city: 'San Francisco',
    state: 'CA',
    postalCode: '94102',
    country: 'US',
    latitude: '37.7849',
    longitude: '-122.4094',
    bedrooms: 3,
    bathrooms: '3.5',
    squareFeet: 3500,
    yearBuilt: 2020,
    amenities: ['private elevator', 'terrace', 'wine cellar', 'smart home', 'valet parking'],
    images: [
      'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00',
      'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688'
    ],
    price: '4200000',
    size: '3500.00',
    status: 'available'
  },
  {
    title: 'Cozy Studio Apartment',
    description: 'Perfect starter home or investment property in a vibrant neighborhood with easy transit access.',
    propertyType: 'apartment' as const,
    address: '321 Castro Street, Unit 4A',
    city: 'San Francisco',
    state: 'CA',
    postalCode: '94114',
    country: 'US',
    latitude: '37.7609',
    longitude: '-122.4350',
    bedrooms: 0,
    bathrooms: '1.0',
    squareFeet: 650,
    yearBuilt: 1985,
    amenities: ['laundry', 'hardwood floors', 'updated kitchen'],
    images: [
      'https://images.unsplash.com/photo-1493809842364-78817add7ffb',
      'https://images.unsplash.com/photo-1502672023488-70e25813eb80'
    ],
    price: '425000',
    size: '650.00',
    status: 'available'
  },
  {
    title: 'Commercial Office Space',
    description: 'Prime commercial real estate in the financial district, perfect for tech companies or startups.',
    propertyType: 'commercial' as const,
    address: '555 Financial Plaza, Floor 8',
    city: 'San Francisco',
    state: 'CA',
    postalCode: '94111',
    country: 'US',
    latitude: '37.7946',
    longitude: '-122.3999',
    squareFeet: 5000,
    yearBuilt: 2015,
    amenities: ['conference rooms', '24/7 security', 'high-speed internet', 'parking'],
    images: [
      'https://images.unsplash.com/photo-1497366216548-37526070297c',
      'https://images.unsplash.com/photo-1497366811353-6870744d04b2'
    ],
    price: '2500000',
    size: '5000.00',
    status: 'available'
  }
];

const sampleListings = [
  {
    propertyId: 1,
    listingType: 'sale' as const,
    price: '850000',
    status: 'active' as const,
    listingDate: new Date('2024-01-15'),
    expiryDate: new Date('2024-07-15'),
    commissionRate: '3.0',
    isFeatured: true,
    viewsCount: 245
  },
  {
    propertyId: 2,
    listingType: 'sale' as const,
    price: '1950000',
    status: 'active' as const,
    listingDate: new Date('2024-01-20'),
    expiryDate: new Date('2024-07-20'),
    commissionRate: '2.5',
    isFeatured: true,
    viewsCount: 189
  },
  {
    propertyId: 3,
    listingType: 'sale' as const,
    price: '4200000',
    status: 'active' as const,
    listingDate: new Date('2024-02-01'),
    expiryDate: new Date('2024-08-01'),
    commissionRate: '2.0',
    isFeatured: true,
    viewsCount: 567
  },
  {
    propertyId: 4,
    listingType: 'rent' as const,
    price: '3200',
    status: 'active' as const,
    listingDate: new Date('2024-02-10'),
    expiryDate: new Date('2024-08-10'),
    commissionRate: '1.0',
    isFeatured: false,
    viewsCount: 78
  },
  {
    propertyId: 5,
    listingType: 'lease' as const,
    price: '8500',
    status: 'active' as const,
    listingDate: new Date('2024-01-25'),
    expiryDate: new Date('2024-07-25'),
    commissionRate: '4.0',
    isFeatured: true,
    viewsCount: 134
  }
];

export async function seed() {
  try {
    console.log('🌱 Starting database seeding...');
    console.log('🔍 DATABASE_URL exists:', !!process.env.DATABASE_URL);
    
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL environment variable is not set');
    }

    // Insert properties
    console.log('🏠 Inserting properties...');
    const insertedProperties = await db.insert(properties).values(sampleProperties).returning();
    console.log(`✅ Inserted ${insertedProperties.length} properties`);

    // Update listing property IDs to match inserted properties
    const updatedListings = sampleListings.map((listing, index) => ({
      ...listing,
      propertyId: insertedProperties[index].id
    }));

    // Insert listings
    console.log('📋 Inserting listings...');
    const insertedListings = await db.insert(listings).values(updatedListings).returning();
    console.log(`✅ Inserted ${insertedListings.length} listings`);

    console.log('🎉 Database seeding completed successfully!');
    console.log('\nSeeded data summary:');
    console.log(`- Properties: ${insertedProperties.length}`);
    console.log(`- Listings: ${insertedListings.length}`);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
}

// Run the seed function if this file is executed directly
if (require.main === module) {
  seed();
}
