import { queryOptions } from '@tanstack/react-query'
import { client } from '@/lib/api-client'

async function fetchOffers() {
  const res = await client.api.listings.$get({
    query: {} // You can add query parameters here as needed
  })
  
  if (!res.ok) {
    throw new Error('Failed to fetch offers')
  }
  
  return res.json()
}

export const offersOptions = queryOptions({
  queryKey: ['offers'],
  queryFn: fetchOffers,
}) 