import { queryOptions } from '@tanstack/react-query'

function getBaseURL() {
  if (typeof window !== 'undefined') {
    return ''
  }
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`
  }
  return 'http://localhost:3000'
}

async function fetchListings() {
  const baseUrl = getBaseURL()
  const res = await fetch(`${baseUrl}/api/listings`, {
    cache: 'no-store',
  })
  
  if (!res.ok) {
    throw new Error('Failed to fetch listings')
  }
  
  return res.json()
}

export const listingsOptions = queryOptions({
  queryKey: ['listings'],
  queryFn: fetchListings,
}) 