import { Badge } from "@/components/ui/badge"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

interface Listing {
  id: number
  title: string
  description?: string
  price: number
  status: 'active' | 'pending' | 'closed'
  createdAt?: string
  updatedAt?: string
}

interface ListingCardProps {
  listing: Listing
}

const statusConfig = {
  active: { 
    label: 'Active', 
    className: 'bg-green-100 text-green-800 hover:bg-green-200' 
  },
  pending: { 
    label: 'Pending', 
    className: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' 
  },
  closed: { 
    label: 'Closed', 
    className: 'bg-gray-100 text-gray-800 hover:bg-gray-200' 
  },
}

export function ListingCard({ listing }: ListingCardProps) {
  const status = statusConfig[listing.status]
  
  return (
    <Card className="h-full transition-all duration-200 hover:shadow-md hover:scale-[1.02]">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <CardTitle className="text-lg">{listing.title}</CardTitle>
            <div className="flex items-center gap-2">
              <span className="text-2xl font-bold text-primary">
                ${listing.price.toLocaleString()}
              </span>
              <Badge variant="secondary" className={status.className}>
                {status.label}
              </Badge>
            </div>
          </div>
        </div>
        {listing.description && (
          <CardDescription className="line-clamp-2">
            {listing.description}
          </CardDescription>
        )}
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>ID: #{listing.id}</span>
          {listing.createdAt && (
            <span>
              Created: {new Date(listing.createdAt).toLocaleDateString()}
            </span>
          )}
        </div>
      </CardContent>
    </Card>
  )
} 