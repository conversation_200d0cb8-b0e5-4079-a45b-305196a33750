'use client'

import { useSuspenseQuery } from '@tanstack/react-query'
import { offersOptions } from '@/lib/queries/offers'
import { OfferCard } from '@/components/offer-card'

interface Offer {
  id: number
  title: string
  description?: string
  price: number
  status: 'active' | 'pending' | 'closed'
  createdAt?: string
  updatedAt?: string
}

export function OffersInfo() {
  const { data: offersData } = useSuspenseQuery(offersOptions)
  
  // The API returns an array of listings directly, transform to match interface
  const offers: Offer[] = (offersData || []).map(listing => ({
    ...listing,
    description: listing.description ?? undefined
  }))

  if (offers.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="rounded-full bg-muted p-3 mb-4">
          <svg
            className="h-6 w-6 text-muted-foreground"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v1M7 7h10"
            />
          </svg>
        </div>
        <h3 className="text-lg font-semibold">No offers found</h3>
        <p className="text-sm text-muted-foreground mt-1">
          There are no offers available at the moment.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Offers</h2>
          <p className="text-muted-foreground">
            {offers.length} offer{offers.length !== 1 ? 's' : ''} available
          </p>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {offers.map((offer) => (
          <OfferCard key={offer.id} offer={offer} />
        ))}
      </div>
    </div>
  )
} 