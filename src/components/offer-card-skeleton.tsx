import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON>Header,
} from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

export function OfferCardSkeleton() {
  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1 flex-1">
            {/* Title skeleton */}
            <Skeleton className="h-6 w-3/4" />
            
            {/* Price and badge skeleton */}
            <div className="flex items-center gap-2 mt-2">
              <Skeleton className="h-8 w-24" />
              <Skeleton className="h-6 w-16 rounded-full" />
            </div>
          </div>
        </div>
        
        {/* Description skeleton */}
        <div className="space-y-2 mt-3">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-2/3" />
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="flex justify-between">
          {/* ID skeleton */}
          <Skeleton className="h-3 w-12" />
          {/* Date skeleton */}
          <Skeleton className="h-3 w-20" />
        </div>
      </CardContent>
    </Card>
  )
} 