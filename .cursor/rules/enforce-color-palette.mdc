---
description:
globs:
alwaysApply: false
---
<rule>
name: enforce-color-palette
description: "Ensures that color values used in styling adhere to the defined project color palette from ui-requirements.md. Helps maintain visual consistency and brand alignment."
filters:
  - type: file_extension
    pattern: "\.(css|scss|less|tsx|jsx|vue|svelte)$"
  # Matches hex codes, rgb, rgba, hsl, hsla. Ignores 'transparent', 'inherit', 'currentColor'.
  # Complex regex to find color definitions not matching the palette.
  # This regex looks for # followed by 3, 4, 6, or 8 hex characters,
  # or rgb/rgba/hsl/hsla functions,
  # but tries to exclude those that ARE in the palette.
  # NOTE: This regex is complex and might need refinement.
  # It's hard to perfectly exclude palette colors within a single regex.
  # A more robust solution might involve a script, but for a .mdc rule, this is an attempt.
  - type: content
    pattern: "(?i)(#(?!FFF(?:FFF)?|F7F7F7|1D1D1F|8E8E93|34C759|FFCC00|FF3B30|007AFF|D1D1D6)([0-9a-f]{3,4}|[0-9a-f]{6}|[0-9a-f]{8})\b|\b(rgb|rgba|hsl|hsla)\((?!\s*(?:255,\s*255,\s*255|247,\s*247,\s*247|29,\s*29,\s*31|142,\s*142,\s*147|52,\s*199,\s*89|255,\s*204,\s*0|255,\s*59,\s*48|0,\s*122,\s*255|209,\s*209,\s*214))[^)]*\))"
actions:
  - type: suggest
    message: |
      A color value was found that does not appear to be part of the approved project color palette.

      **Approved Color Palette (from ui-requirements.md):**
      - White: `#FFFFFF`
      - Off-White: `#F7F7F7`
      - Dark Grey: `#1D1D1F`
      - Medium Grey: `#8E8E93`
      - Accent Gradient: `linear-gradient(135deg, #0EA5E9, #3B82F6)` (Note: Gradients are harder to check with this rule)
      - Success: `#34C759`
      - Warning: `#FFCC00`
      - Error: `#FF3B30`
      - Info: `#007AFF`
      - Input Border: `#D1D1D6` (from Inputs & Forms section)

      Consider using a CSS variable or a pre-defined color from the palette. If this is a new, intentional color, please update the `ui-requirements.md` and this rule.

      **Matched pattern:** `{{match}}`

      Using predefined color variables (e.g., via CSS Custom Properties or a theme object) is highly recommended over hardcoding hex values.
examples:
  - input: |
      // Bad: Hardcoded color not in palette
      .my-component {
        background-color: #123456;
        color: rgb(100, 100, 100);
      }
    output: "Suggest using a color from the approved palette."
  - input: |
      // Good: Using approved colors (or CSS variables representing them)
      .my-component {
        background-color: var(--color-off-white); /* or #F7F7F7 */
        color: #1D1D1F;
      }
    output: "Adheres to color palette."
metadata:
  priority: high
  version: 1.0
  source_doc: "ui-requirements.md#color-palette"
  known_limitations: "The regex for detecting non-palette colors is complex and may have false positives/negatives, especially with shorthand hex or complex color functions. It does not effectively check gradients. Using CSS variables or design tokens is a more robust way to manage colors."
</rule>
