---
description: 
globs: 
alwaysApply: false
---
<rule>
name: enforce-next-image-usage
description: "Encourages the use of the Next.js `Image` component (`next/image`) instead of raw `<img>` tags for better performance and image optimization, as per tech-requirements.md."
filters:
  - type: file_extension
    pattern: "\.(tsx|jsx)$"
  - type: content
    # Matches <img> tags but tries to avoid matching if next/image is imported
    # and the component is named <Image ...> (case sensitive).
    # This is a heuristic.
    pattern: "(?!.*import Image from 'next/image')<img\s+[^>]*src=['\"][^>]*>"
actions:
  - type: suggest
    message: |
      Found a raw `<img>` tag. Consider using the Next.js `Image` component from `next/image` for automatic image optimization (resizing, modern formats, lazy loading).

      **Tech Requirement (from tech-requirements.md - SEO & Performance Optimizations):**
      - "Image Optimization: Leverage `next/image` with `loader` configured for Cloudflare/Imgix; modern formats (WebP/AVIF)."

      **Example:**
      ```tsx
      // Potentially problematic:
      <img src="/path/to/image.jpg" alt="Description" width="500" height="300" />

      // Better (using next/image):
      import Image from 'next/image';

      <Image src="/path/to/image.jpg" alt="Description" width={500} height={300} />
      ```

      Ensure `next/image` is configured correctly in `next.config.js` if using external image sources (domains) or custom loaders.
      If this `<img>` tag is used for a specific reason where `next/image` is not suitable (e.g., SVGs that don't need optimization, or very specific use cases), you can ignore this suggestion.
examples:
  - input: |
      // Bad: Raw img tag
      const MyComponent = () => {
        return <img src="/logo.png" alt="Logo" />;
      };
    output: "Suggest using next/image component."
  - input: |
      // Good: Using next/image
      import Image from 'next/image';
      const MyComponent = () => {
        return <Image src="/logo.png" alt="Logo" width={100} height={50} />;
      };
    output: "Uses next/image component."
metadata:
  priority: medium
  version: 1.0
  source_doc: "tech-requirements.md#5-seo--performance-optimizations"
  known_limitations: "The regex is a heuristic and might not catch all cases or could have false positives, especially if `next/image` is imported differently or the component is aliased. It doesn't check for correct `width` and `height` props on `next/image` which are often required."
</rule>
