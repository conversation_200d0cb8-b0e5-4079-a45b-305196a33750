---
description: 
globs: 
alwaysApply: false
---
<rule>
name: check-accessibility-attributes
description: "Promotes better accessibility by suggesting the use of ARIA attributes (e.g., `aria-label`, `role`) on interactive or non-obvious elements, as per ui-requirements.md."
filters:
  - type: file_extension
    pattern: "\.(tsx|jsx|vue|svelte|html)$"
  # Looks for common interactive elements or elements that often need ARIA, without these attributes.
  # This is a heuristic and not exhaustive.
  - type: content
    # Example: Matches button, a, input, img, svg, or elements with onClick that LACK common ARIA labels or role.
    # This regex is illustrative and would need significant refinement for production use.
    pattern: "<(button|a|input|img|svg|div|span)(?!.*(aria-label=|aria-labelledby=|role=|alt=))[^>]*>(?!.*</\1>)"
    # A more specific pattern focusing on elements with event handlers:
    # pattern: "<(?!(button|a|input|select|textarea))[^>]+(onClick|onKeyPress|onTap)[^>]*?(?<!aria-label=)(?<!aria-labelledby=)(?<!role=)>"
actions:
  - type: suggest
    message: |
      Found an element that might require ARIA attributes for accessibility.

      **Context from `ui-requirements.md` (Responsive & Accessibility):**
      - "ARIA & Keyboard: All components fully navigable; ARIA labels for icons and dynamic regions."

      Consider adding attributes like `aria-label` (for elements without visible text), `aria-labelledby` (to associate with existing text), or `role` (to define the element's purpose, e.g., `role='button'` for a styled `div` acting as a button).

      For images (`<img>`), ensure an `alt` attribute is present and descriptive. For SVGs used as icons, `aria-hidden='true'` might be appropriate if decorative, or `role='img'` and `aria-label` if conveying information.

      **Example:**
      ```html
      // Potentially problematic (if icon provides meaning and has no text)
      <button onClick={handler}><Icon type="settings" /></button>

      // Better
      <button onClick={handler} aria-label="Settings"><Icon type="settings" /></button>
      ```

      Ensuring elements are keyboard navigable and have clear roles and labels is crucial for users relying on assistive technologies.

      **Matched element:** `{{match}}`

      If this element is purely decorative and has no interactive function, or if ARIA attributes are handled by a parent component, this suggestion might be a false positive.
examples:
  - input: |
      // Bad: Icon button without ARIA label
      <button onClick={doSomething}><MyIcon /></button>

      // Bad: Image without alt text
      <img src="important_diagram.png" />

      // Bad: Div used as button without role or ARIA attributes
      <div onClick={handleClick}>Click me</div>
    output: "Suggest adding appropriate ARIA attributes (aria-label, role, alt)."
  - input: |
      // Good: Icon button with ARIA label
      <button aria-label="Perform action" onClick={doSomething}><MyIcon /></button>

      // Good: Image with alt text
      <img src="important_diagram.png" alt="Diagram showing system architecture" />

      // Good: Div used as button with role and tabindex
      <div role="button" tabIndex="0" onClick={handleClick} onKeyPress={handleKeyPress}>Click me</div>
    output: "Element includes necessary accessibility attributes."
metadata:
  priority: medium
  version: 1.0
  source_doc: "ui-requirements.md#responsive--accessibility"
  known_limitations: "The regex is a heuristic and cannot fully determine accessibility compliance. It may produce false positives or miss cases. Manual testing and dedicated accessibility audit tools are essential."
</rule>
