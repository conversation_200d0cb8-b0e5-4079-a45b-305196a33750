---
description: 
globs: 
alwaysApply: false
---
<rule>
name: enforce-ui-component-usage
description: "Encourages the use of pre-defined UI components from `src/components/ui` for UI consistency and maintainability, as per product design principles. Avoid direct use of generic HTML elements like `<button>`, `<div>` for complex UI structures, or input elements where a custom component from the UI library is available."
filters:
  - type: file_extension
    pattern: "\.(tsx|jsx)$"
  # Example: Discourage direct usage of <button> if a Button component exists.
  # This is a simple example; more sophisticated checks might be needed.
  - type: content
    pattern: "(<button[^>]*>|<div>\s*<input)" # A simplistic pattern to catch raw button or a div wrapping an input that might be a custom component
actions:
  - type: suggest # Could be 'reject' for stricter enforcement
    message: |
      Consider using a component from the `src/components/ui` library instead of raw HTML elements like `<button>`, `<div>`, or `<input>` for better consistency and adherence to the design system.

      For example, if you have a `Button` component in `src/components/ui/Button.tsx`, prefer:
      ```tsx
      import { Button } from '@/components/ui/Button';
      // ...
      <Button>Click Me</Button>
      ```
      Over:
      ```tsx
      <button>Click Me</button>
      ```
      Ensure that imports for UI components point to `@/components/ui/...`.

      **Rationale (from prod-requirements.md):**
      - "Consistency: Unified design system and component library"
      - "Minimalist Aesthetic: Ample white space, clear typography, limited color palette"
      - "Mobile-First: Responsive layouts, thumb-friendly controls"

      If this usage is intentional or a suitable UI component does not exist, you can ignore this suggestion.
examples:
  - input: |
      // Bad: Direct use of HTML button
      const MyComponent = () => {
        return <button onClick={() => console.log('clicked')}>Submit</button>;
      };

      // Bad: Direct use of HTML div and input for a form field
      const MyForm = () => {
        return <div><label>Name:</label><input type="text" /></div>
      }
    output: "Suggest using a UI component from src/components/ui instead."
  - input: |
      // Good: Using a Button component from the UI library
      import { Button } from '@/components/ui/Button';
      const MyComponent = () => {
        return <Button onPress={() => console.log('clicked')}>Submit</Button>;
      };

      // Good: Using a FormField component (hypothetical)
      import { FormField } from '@/components/ui/FormField';
      const MyForm = () => {
        return <FormField label="Name" type="text" />
      }
    output: "Correct usage of UI components."
metadata:
  priority: medium
  version: 1.0
  source_doc: "prod-requirements.md#10-uiux--design-principles"
</rule>
