---
description:
globs:
alwaysApply: false
---
<rule>
name: enforce-zod-validation-in-api
description: "Encourages the use of Zod for input and output validation in API routes, as per tech-requirements.md. Ensures data integrity and type safety."
filters:
  - type: file_path # Using file_path to target API route files
    # Targets route.ts files within any subdirectory of src/app/api for App Router
    # and any .ts files within src/pages/api for Pages Router.
    pattern: "(src/app/api/.*/route\.ts|src/pages/api/.*\.ts)$"
  - type: content
    # This pattern looks for typical API handler function definitions (e.g., GET, POST)
    # and checks if 'zod' or 'Zod' is NOT imported or heavily used in the file.
    # This is a heuristic and might need refinement.
    # It checks for absence of `import.*zod` and common Zod schema methods.
    pattern: "^(?!.*(import.*from\s*['\"]zod['\"]|z\.object|z\.string|z\.number|ZodError)).*\b(export\s+async\s+function\s+(GET|POST|PUT|DELETE|PATCH)|const\s+(GET|POST|PUT|DELETE|PATCH)\s*[:=])"
    is_multiline: true # Allows ^ to match start of lines for the negative lookahead
actions:
  - type: suggest
    message: |
      This API route file (`{{filepath}}`) does not appear to import or use Zod for schema validation.

      **Tech Requirement (from tech-requirements.md):**
      - "API Layer: Hono framework on the edge ... input/output validated with Zod schemas."
      - "API Design: ... Inputs and outputs validated with Zod; errors returned in standardized shape."
      - "Type Validation: Zod for runtime schema validation on both client and server."

      Please ensure that request inputs (e.g., query parameters, request body) and response outputs are validated using Zod schemas.

      **Example (Conceptual):**
      ```typescript
      import { z } from 'zod';
      import { NextResponse } from 'next/server';

      const QuerySchema = z.object({
        id: z.string().cuid(),
      });

      export async function GET(request: Request) {
        const { searchParams } = new URL(request.url);
        const queryParams = Object.fromEntries(searchParams.entries());

        try {
          const validatedQuery = QuerySchema.parse(queryParams);
          // ... process validatedQuery.id
          return NextResponse.json({ data: `Property ${validatedQuery.id}` });
        } catch (error) {
          if (error instanceof z.ZodError) {
            return NextResponse.json({ error: error.errors }, { status: 400 });
          }
          return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
        }
      }
      ```
      Using Zod helps maintain data integrity, provides clear error messages, and ensures type safety across your API layer.
examples:
  - input: |
      // File: src/app/api/items/route.ts
      // Bad: API route without Zod validation
      import { NextResponse } from 'next/server';

      export async function POST(request: Request) {
        const body = await request.json();
        // No validation of body
        console.log(body.name);
        return NextResponse.json({ message: 'Item created' });
      }
    output: "Suggests adding Zod for input/output validation."
  - input: |
      // File: src/app/api/items/route.ts
      // Good: API route with Zod validation
      import { z } from 'zod';
      import { NextResponse } from 'next/server';

      const ItemSchema = z.object({
        name: z.string().min(1),
        price: z.number().positive(),
      });

      export async function POST(request: Request) {
        try {
          const body = await request.json();
          const validatedItem = ItemSchema.parse(body);
          // Process validatedItem
          return NextResponse.json({ data: validatedItem, message: 'Item created' });
        } catch (error) {
          if (error instanceof z.ZodError) {
            return NextResponse.json({ error: error.issues }, { status: 400 });
          }
          return NextResponse.json({ error: 'Server error' }, { status: 500 });
        }
      }
    output: "Zod validation is present."
metadata:
  priority: high
  version: 1.0
  source_doc: "tech-requirements.md#2-technology-stack"
  known_limitations: "The regex for detecting Zod usage is a heuristic. It checks for imports and some common Zod methods but might not catch all Zod usage patterns or could have false positives/negatives. Complex validation logic might not be detected."
</rule>
