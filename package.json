{"name": "homeo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "postinstall": "drizzle-kit generate", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:seed": "tsx src/lib/db/seed/index.ts"}, "dependencies": {"@clerk/nextjs": "^6.20.0", "@hono/zod-validator": "^0.5.0", "@neondatabase/serverless": "^1.0.0", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-navigation-menu": "^1.2.10", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.12", "@tanstack/react-query": "^5.75.0", "@tanstack/react-query-devtools": "^5.75.0", "@tanstack/react-query-next-experimental": "^5.75.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-orm": "^0.43.1", "hono": "^4.7.8", "lucide-react": "^0.504.0", "next": "15.3.1", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.4.5", "drizzle-kit": "^0.31.1", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "tsx": "^4.19.2", "tw-animate-css": "^1.2.8", "typescript": "^5"}}