## UI Requirements Document: MinimalEstate Platform

### Apple Design Philosophy & Implementation

Adopting Apple’s principles of clarity, deference, and depth ensures a minimalist, intuitive interface for property search, listing, and management.

#### 1. Clarity

* **Content Primacy**: Property details (images, price, location) take center stage; UI chrome is minimal.
* **Functional Minimalism**: Generous negative space guides the eye to key elements—search bar, filters, call-to-actions—without decoration.
* **Intentional Hierarchy**: Use typography scale and color contrasts to emphasize property titles, prices, and action buttons.
* **Purposeful Animation**: Subtle fade-ins for listing cards and smooth transitions when toggling map/list views provide context.

#### 2. Deference

* **Content-Forward Design**: UI controls (buttons, filters) are understated, allowing property photos and descriptions to stand out.
* **Background Subtlety**: Soft translucent overlays behind modals and drawers create focus without distraction.
* **Translucency**: Use slight background blur under navigation drawers to imply depth.
* **Intuitive Gestures**: On mobile, swipe to favorite, pinch to zoom on images, and tap-and-hold for quick property card actions.

#### 3. Depth

* **Layering**: Define clear layers: background map/list, content cards, modals/dialogs, ensuring separation via shadows and z-index.
* **Contextual Transitions**: Card to detail-view transitions expand from the card’s position with soft scaling and opacity shifts.
* **Dimensionality**: Implement subtle shadows (2–4px blur, 1px y-offset, low opacity) under cards and buttons.

---

### Color Palette

| Type                | Value                                     | Usage                                |
| ------------------- | ----------------------------------------- | ------------------------------------ |
| **White**           | #FFFFFF                                   | Primary background                   |
| **Off-White**       | #F7F7F7                                   | Secondary panels and cards           |
| **Dark Grey**       | #1D1D1F                                   | Primary text                         |
| **Medium Grey**     | #8E8E93                                   | Secondary text                       |
| **Accent Gradient** | linear-gradient(135deg, #0EA5E9, #3B82F6) | Primary actions, links, focus states |
| **Success**         | #34C759                                   | Confirmation messages, success icons |
| **Warning**         | #FFCC00                                   | Alerts, warnings                     |
| **Error**           | #FF3B30                                   | Error states, invalid inputs         |
| **Info**            | #007AFF                                   | Info highlights, tooltips            |

---

### Typography

* **Font Family**: SF Pro Display for headings (fallback: Inter); SF Pro Text for body (fallback: Roboto).
* **Weights**: Bold (700) for headings, Semibold (600) for subheadings, Regular (400) for body, Medium (500) for labels.
* **Sizes & Scale**:

  * Large Title / Hero: 32px
  * Title 1: 24px
  * Title 2 / Card Title: 20px
  * Headline / Section Title: 18px
  * Body Text: 16px
  * Small Text / Meta: 14px
  * Caption: 12px
* **Letter Spacing**: -0.3px on headings, normal on body.
* **Line Heights**: 1.2× for headings, 1.5× for body.

---

### Core Components

#### Buttons

* **Primary**: Accent gradient background, white text, pill shape (6px radius), 44px height.
* **Secondary**: Light grey background, dark grey text, 6px radius.
* **Tertiary**: Text-only links in accent color, underline on hover.
* **Icon Buttons**: 32×32px with 24px SF Symbol; monochrome, toggles accent when active.

#### Inputs & Forms

* Single-line inputs with 1px border (#D1D1D6), 6px radius; label animates above on focus.
* Error state: border turns #FF3B30 and shakes subtly.
* Field spacing: 16px vertical margin.

#### Cards

* Background: white (#FFFFFF), radius: 8px, shadow: 2px blur @5% opacity.
* Padding: 16px; image top, content below.
* On hover: slight scale-up (102%) and deeper shadow (4px blur).

#### Modals & Drawers

* Centered or slide-up on mobile; backdrop blur (10px) behind.
* Max width: 600px; padding: 24px.
* Close icon top-right, 24px tappable area.

---

### Visual Elements

#### SF Symbols & Iconography

* Use Apple SF Symbol equivalents for common actions (search, filter, favorite).
* 24px size by default, 20px in toolbars.
* Apply accent color on active/in-focus states.

#### Illustrations & Abstract Shapes

* Occasional use of low-opacity (8–12%) abstract blobs in accent gradient for empty states and hero banners.
* SVG-based for scalability; subtle parallax on scroll.

#### Microinteractions

* Button press: scale to 98% over 100ms.
* Card hover: elevate shadow and scale.
* Form focus: input label transitions in 150ms.
* Favorite toggle: heart icon bounce on toggle.

---

### Page-Specific Guidelines

#### Home / Search Page

* Hero search bar prominently centered on wide screens.
* Filters collapse into a bottom sheet on mobile.
* Listings grid with 2 columns on desktop, 1 on mobile.
* Map toggle button sticky at top-right; high contrast icon.

#### Property Detail Page

* Carousel with fullscreen swipe support.
* Prominent price and key details under hero image.
* Sticky footer with "Contact" and "Schedule Visit" buttons.
* Tabbed sections: Details, Location, Reviews.

#### Listing Wizard

* Multi-step progress indicator at top (steps 1–4).
* Steps displayed in a modal-like centered container.
* Save & Continue buttons in right rail on desktop, bottom on mobile.

#### Dashboard (Pro & Non-Pro)

* Sidebar navigation with icons and labels.
* Stats cards at top: Listings count, Inquiries, Completed Transactions.
* Table view for listings: sortable columns, inline actions.

---

### Responsive & Accessibility

* **Mobile-First**: Design at 375px viewport; scale up with breakpoints at 768px and 1024px.
* **Touch Targets**: Minimum 44×44px interactive elements.
* **Contrast**: Text meets 4.5:1 ratio; large text 3:1 acceptable.
* **Reduced Motion**: Respect OS settings; reduce animation durations to zero if requested.
* **ARIA & Keyboard**: All components fully navigable; ARIA labels for icons and dynamic regions.

---

**End of UI Requirements Document**
