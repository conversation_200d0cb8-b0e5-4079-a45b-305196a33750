## 1. Purpose

This Technical Requirements Document defines the architecture, technologies, and non-functional requirements for the MVP of MinimalEstate. It translates business and product requirements into a concrete development plan, ensuring alignment across engineering, DevOps, and QA teams.

---

## 2. Technology Stack

* **Frontend Framework**: Next.js (v14) for SSR, SSG, and hybrid rendering.
* **Data Fetching & Caching**: TanStack Query for server and client data synchronization, caching, and mutation management.
* **Type Validation**: <PERSON><PERSON> for runtime schema validation on both client and server.
* **Forms**: React Hook Form for performant, reusable form state management.
* **State Management**: Zustand for global client state (e.g., auth, UI preferences).
* **Styling & Components**: Tailwind CSS utility-first styling, with shadcn/ui for prebuilt accessible components.
* **ORM & Database**: Prisma for type-safe database access; NeonDB (PostgreSQL-compatible serverless) as primary data store.
* **API Layer**: Hono framework on the edge for REST API endpoints, leveraging its high-performance routing and middleware support; input/output validated with Zod schemas.
* **Authentication/Authorization**: Better Auth for robust, scalable auth flows, providing JWT-based session management and role-based access control via Hono middleware.
* **Search Engine Optimization**: Next.js built-in SEO features (head tags, dynamic sitemaps), along with `next-seo` library for standardized metadata configuration.
* **CI/CD & Infrastructure**: Vercel for frontend and edge function deployment; GitHub Actions for testing & linting; Neon-hosted database.
* **Image Handling**: `next/image` component with on-the-fly optimization; Cloudflare Images or Imgix integration.
* **Environment Management**: `dotenv` for local env vars, Vercel Environment Variables.
* **Monitoring & Logging**: Sentry for error tracking; LogRocket or Datadog for session replay and performance logs.
* **Testing**: Jest with React Testing Library for unit/component tests; Playwright for end-to-end flows.
* **Linting & Formatting**: ESLint (with Next.js plugin), Prettier, and Zod plugin for type inference.
* **Accessibility**: axe-core integrated in CI to catch a11y regressions.

---

## 3. Architecture & Folder Structure

```
/ ─ root
│
├── .github/               # CI workflows (tests, lint, deploy)
├── prisma/                # Prisma schema and migrations
│   └── schema.prisma
├── public/                # Static assets (robots.txt, favicon, static images)
├── src/
│   ├── pages/             # Next.js pages (SSR/SSG/ISR routes)
│   │   ├── index.tsx
│   │   ├── api/           # API routes
│   │   └── _____
│   ├── components/        # Reusable UI components (shadcn customization)
│   ├── hooks/             # Custom React hooks (useAuth, useUser)
│   ├── lib/               # Shared libraries (trpc client, api clients)
│   ├── store/             # Zustand stores
│   ├── styles/            # Tailwind overrides & global styles
│   └── utils/             # Helper functions (date formatting, URLs)
├── tests/                 # E2E tests (Playwright)
├── jest.config.js
├── next.config.js         # Next.js config (image domains, i18n, rewrites)
├── tailwind.config.js     # Tailwind customization
└── tsconfig.json          # TypeScript config
```

* **Server-Side Data Fetching**: Use `getServerSideProps` for user-specific pages, `getStaticProps` with ISR for public listings and sitemaps.
* **API Design**: Endpoints structured under `/api/*`. Inputs and outputs validated with Zod; errors returned in standardized shape.

---

## 4. Non-Functional Requirements

| Category        | Requirement                                                                                       |
| --------------- | ------------------------------------------------------------------------------------------------- |
| Performance     | - TTFB ≤ 200ms for cached pages<br>- CLS ≤ 0.1, LCP ≤ 2.5s, FID ≤ 100ms (Core Web Vitals targets) |
| Scalability     | - Handle spike of 10k concurrent users with horizontal scale on Vercel<br>- DB autoscaling rules  |
| Security        | - OWASP Top 10 protections<br>- HTTPS-only cookies, CSP header<br>- Rate limiting on API routes   |
| SEO             | - Auto-generated XML sitemap; dynamic `robots.txt`<br>- Structured data (JSON-LD) for listings    |
| Reliability     | - 99.9% uptime SLA<br>- Automated health checks and alerts                                        |
| Maintainability | - 90% test coverage on unit and integration tests<br>- Enforce code style via CI                  |
| Accessibility   | - WCAG 2.1 AA compliance; axe checks in CI                                                        |
| Observability   | - Sentry errors < 1 per 1k user sessions<br>- Monitor Next.js build times and bundle sizes        |

---

## 5. SEO & Performance Optimizations

1. **Metadata Management**: Use `next-seo` for default and per-page meta tags, Open Graph, and Twitter cards.
2. **Sitemap & Robots**: Generate sitemap.xml dynamically at build time; serve robots.txt via static file.
3. **Structured Data**: JSON-LD for property listings, including `Offer`, `Place`, `AggregateRating`.
4. **Image Optimization**: Leverage `next/image` with `loader` configured for Cloudflare/Imgix; modern formats (WebP/AVIF).
5. **Critical CSS**: Tailwind purge in production to remove unused classes.
6. **Preconnect & Prefetch**: `<link rel='preconnect'>` for API and image domains; `next/link` for route prefetching.
7. **Loading Strategies**: Use `loading='lazy'` on non-critical images; defer non-essential JS.

---

## 6. Data Model & API Contracts

### 6.1. Prisma Schema (excerpt)

```prisma
model Property {
  id          String   @id @default(cuid())
  title       String
  description String
  price       Int
  status      ListingStatus
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  ownerId     String
  owner       User     @relation(fields: [ownerId], references: [id])
}

enum ListingStatus {
  DRAFT
  PUBLISHED
  SOLD
}
```

### 6.2. Example API Route: `GET /api/properties`

* **Query Params**: `?status=published&minPrice=...&maxPrice=...&location=...` validated with Zod schema.
* **Response**: JSON array of `Property` objects, serialized with ISO date strings.
* **Caching**: Set `Cache-Control: public, max-age=60, stale-while-revalidate=300` for list endpoints.

---

## 7. Development & Deployment Workflow

1. **Branching Strategy**: `main` for production, `develop` for integration, feature branches per ticket.
2. **CI Pipeline (GitHub Actions)**:

   * On PR: `npm ci`, `lint`, `type-check`, `test:unit`, `test:lint:a11y`
   * On merge to `main`: Build & deploy to Vercel; run integration tests.
3. **Release Process**: Semantic versioning; release tags; automated changelog via Conventional Commits.

---

## 8. Monitoring & Alerting

* **Error Tracking**: Sentry integrated in both client and server; auto-issue assignment.
* **Performance Metrics**: Real User Monitoring (RUM) via Vercel Analytics; custom dashboards in Datadog.
* **Logging**: Structured logs in Cloudflare Workers (if used) or Vercel Logs; central aggregation in Logflare.
* **Alerts**: PagerDuty notifications for Sentry critical issues and CI failures.

---

## 9. Security & Compliance

* **Authentication**: NextAuth.js with OAuth providers; enforce secure cookies.
* **Authorization**: Middleware to guard API routes; role-based checks in services.
* **Data Protection**: AES-256 encryption for sensitive fields; GDPR data deletion workflows.
* **Penetration Testing**: Quarterly pentests; dependency vulnerability scans (Dependabot).

---

## 10. Future Considerations

* Expand to microservice architecture for high-load modules (search, messaging).
* Add GraphQL layer via Apollo Federation.
* Implement AI-driven recommendations and chatbots.
* Multi-region database replication for global scale.
